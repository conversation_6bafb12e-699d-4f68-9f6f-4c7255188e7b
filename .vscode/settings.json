{
    "maven.view": "hierarchical",
    "java.compile.nullAnalysis.mode": "automatic",
    "java.jdt.ls.vmargs": "-XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 -Dsun.zip.disableMemoryMapping=true -Xmx8G -Xms100m -Xlog:disable",
    "files.autoSave": "afterDelay",
    "java.search.scope": "all",
    // 编辑区域设置
    "editor.renderWhitespace": "none",  // 不渲染空格
    "editor.suggestSelection": "first",  // 建议选择第一个选项
    "editor.suggest.showReferences": false,  // 不显示建议的引用
    "editor.renderControlCharacters": false,  // 不渲染控制字符
    "editor.stickyScroll.enabled": false,  // 禁用粘性滚动
    "workbench.editor.enablePreview": false, // 关闭预览
    "editor.smoothScrolling": true,  // 启用平滑滚动
    // "editor.cursorBlinking": "expand",  // 扩展光标闪烁
    // "editor.cursorSmoothCaretAnimation": "on",  // 启用光标平滑移动动画
    "workbench.list.smoothScrolling": true,  // 列表平滑滚动
    "explorer.decorations.colors": true,
    "explorer.decorations.badges": true,
    "git.decorations.enabled": true,
    "git.decorations.colors": true,
    "java.configuration.updateBuildConfiguration": "interactive"
}