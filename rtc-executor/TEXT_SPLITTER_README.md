# 文本拆句工具 (Text Splitter)

## 概述

这是一个智能的中文文本拆句工具，能够根据标点符号和长度要求将长文本拆分成多个合适的句子。

## 功能特点

1. **智能拆分**: 优先按照标点符号进行拆句，避免断句
2. **长度控制**: 每句最大400汉字，最少10汉字
3. **句子最少**: 在满足长度要求的前提下，尽可能减少句子数量
4. **多级标点**: 支持主要标点符号（。！？；）、次要标点符号（，、：）和其他符号
5. **强制拆分**: 当无法找到合适标点时，进行智能强制拆分
6. **后处理优化**: 自动合并过短的句子

## 核心类说明

### 1. TextSplitter
核心拆句引擎，提供基础的文本拆分功能。

### 2. TextSplitterUtil
简化版API，提供更便捷的调用接口。

### 3. SplitResult
拆分结果封装类，包含统计信息和验证功能。

## 使用方法

### 基础使用

```java
import com.mantis.TextSplitterUtil;
import java.util.List;

// 简单拆分
String text = "这是一个很长的文本...";
List<String> sentences = TextSplitterUtil.split(text);

// 打印结果
for (int i = 0; i < sentences.size(); i++) {
    System.out.println((i + 1) + ". " + sentences.get(i));
}
```

### 带统计信息的使用

```java
import com.mantis.TextSplitterUtil;

String text = "这是一个很长的文本...";
TextSplitterUtil.SplitResult result = TextSplitterUtil.splitWithStats(text);

// 打印详细统计
result.printStats();

// 获取简要统计
System.out.println(result.getSummary());

// 验证结果
if (result.isValid()) {
    System.out.println("所有句子都符合长度要求");
}
```

### 批量处理

```java
String[] texts = {"文本1", "文本2", "文本3"};
TextSplitterUtil.SplitResult[] results = TextSplitterUtil.batchSplit(texts);

for (TextSplitterUtil.SplitResult result : results) {
    System.out.println(result.getSummary());
}
```

### 检查是否需要拆分

```java
String text = "待检查的文本";
if (TextSplitterUtil.needsSplit(text)) {
    // 需要拆分
    List<String> sentences = TextSplitterUtil.split(text);
} else {
    // 无需拆分
    System.out.println("文本长度合适");
}
```

## 拆分规则

### 标点符号优先级

1. **主要标点符号**（优先级最高）: 。！？；
2. **次要标点符号**（优先级中等）: ，、：
3. **其他标点符号**（优先级较低）: ）】」』"》

### 拆分策略

1. 在目标位置附近寻找最佳拆分点
2. 优先选择主要标点符号
3. 如无主要标点，选择次要标点符号
4. 如无合适标点，进行强制拆分
5. 验证拆分结果是否符合长度要求

### 后处理规则

1. 合并过短的句子（少于10汉字）
2. 确保合并后不超过最大长度限制
3. 保持语义的相对完整性

## 配置参数

- **MAX_LENGTH**: 400 (每句最大汉字数)
- **MIN_LENGTH**: 10 (每句最少汉字数)

## 测试用例

项目包含了多个测试类：

1. **TextSplitterDemo**: 基础功能演示
2. **TextSplitterAdvancedTest**: 超长文本测试
3. **TextSplitterUtilDemo**: 工具类使用示例

### 运行测试

```bash
# 基础测试
mvn compile exec:java -Dexec.mainClass="com.mantis.TextSplitterDemo"

# 高级测试
mvn compile exec:java -Dexec.mainClass="com.mantis.TextSplitterAdvancedTest"

# 工具类示例
mvn compile exec:java -Dexec.mainClass="com.mantis.TextSplitterUtilDemo"
```

## 性能特点

- **高效处理**: 单次遍历完成拆分
- **内存友好**: 避免大量字符串复制
- **智能优化**: 减少不必要的拆分操作

## 适用场景

1. **语音合成**: 将长文本拆分为适合TTS的句子
2. **文本处理**: 自然语言处理的预处理步骤
3. **内容展示**: 网页或应用中的文本分段显示
4. **翻译系统**: 将长文本拆分为翻译单元
5. **文档处理**: 自动化文档分段

## 注意事项

1. 主要针对中文文本设计，对英文等其他语言支持有限
2. 汉字统计基于Unicode范围 `\u4e00-\u9fa5`
3. 标点符号识别基于预定义字符集
4. 建议在使用前进行充分测试以确保符合具体需求

## 扩展建议

1. 支持自定义长度参数
2. 增加更多语言支持
3. 提供更多标点符号配置选项
4. 增加语义分析功能
5. 支持更复杂的拆分规则
