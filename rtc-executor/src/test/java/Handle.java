import com.mantis.rtc.utils.AudioResampler;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;

public class Handle {
    public static void main(String[] args) {
        try {
            // 源文件路径
            String sourceFilePath = "/Users/<USER>/mantis_codes/aigc/micro-rtc-service/rtc-executor/src/test/java/ai_1751514221344.pcm";
            // 目标文件路径
            String targetFilePath = "user_new2.pcm";
            
            // 每次读取的字节数：1920字节（对应480个采样点，双声道，每个采样点2字节）
            final int CHUNK_SIZE = 1920;
            
            // 创建文件输入输出流
            try (FileInputStream fis = new FileInputStream(sourceFilePath);
                 FileOutputStream fos = new FileOutputStream(targetFilePath)) {
                
                byte[] buffer = new byte[CHUNK_SIZE];
                int bytesRead;
                int totalProcessed = 0;
                int chunkCount = 0;
                
                System.out.println("开始音频重采样处理...");
                System.out.println("每次处理: " + CHUNK_SIZE + " 字节");
                
                // 逐块读取并处理
                while ((bytesRead = fis.read(buffer)) > 0) {
                    chunkCount++;
                    
                    // 如果读取的字节数不足CHUNK_SIZE，创建新的数组
                    byte[] chunkData;
                    if (bytesRead < CHUNK_SIZE) {
                        chunkData = new byte[bytesRead];
                        System.arraycopy(buffer, 0, chunkData, 0, bytesRead);
                    } else {
                        chunkData = buffer;
                    }
                    
                    // 验证源数据格式
                    if (!AudioResampler.isValidSourceFormat(chunkData)) {
                        System.err.println("第 " + chunkCount + " 块数据格式无效，跳过处理");
                        continue;
                    }
                    
                    // 执行音频重采样
                    byte[] resampledData = AudioResampler.resample(chunkData);
                    
                    // 写入目标文件
                    fos.write(resampledData);
                    
                    totalProcessed += bytesRead;
                    
                    // 输出处理进度
                    if (chunkCount % 100 == 0) {
                        System.out.println("已处理 " + chunkCount + " 块，累计 " + totalProcessed + " 字节");
                    }
                }
                
                System.out.println("音频重采样完成！");
                System.out.println("总共处理: " + chunkCount + " 块");
                System.out.println("源文件总大小: " + totalProcessed + " 字节");
                
                // 计算目标文件大小
                long targetFileSize = Files.size(Paths.get(targetFilePath));
                System.out.println("目标文件大小: " + targetFileSize + " 字节");
                
                // 计算压缩比
                double compressionRatio = (double) targetFileSize / totalProcessed;
                System.out.println("压缩比: " + String.format("%.2f", compressionRatio * 100) + "%");
                
            } catch (IOException e) {
                System.err.println("文件操作失败: " + e.getMessage());
                e.printStackTrace();
            }
            
        } catch (Exception e) {
            System.err.println("音频重采样失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
