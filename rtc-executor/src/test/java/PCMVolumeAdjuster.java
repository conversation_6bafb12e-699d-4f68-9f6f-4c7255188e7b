import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;

public class PCMVolumeAdjuster {
    private static final float GAIN = 0.3f; // 音量减半

    public static void main(String[] args) throws IOException {
        File inputFile = new File("/Users/<USER>/mantis_codes/aigc/micro-rtc-service/rtc-executor/src/test/java/test_submit-8k.pcm");
        File outputFile = new File("/Users/<USER>/mantis_codes/aigc/micro-rtc-service/rtc-executor/src/test/java/output.pcm");
        
        try (FileInputStream fis = new FileInputStream(inputFile);
             FileOutputStream fos = new FileOutputStream(outputFile)) {
            
            byte[] buffer = new byte[4096];
            int bytesRead;
            while ((bytesRead = fis.read(buffer)) != -1) {
                adjustVolume(buffer, bytesRead, GAIN);
                fos.write(buffer, 0, bytesRead);
            }
        }
    }

    private static void adjustVolume(byte[] data, int length, float gain) {
        for (int i = 0; i < length; i += 2) {
            short sample = (short) ((data[i] & 0xFF) | (data[i+1] << 8));
            sample = (short) (sample * gain);
            sample = (short) Math.max(-32768, Math.min(32767, sample));
            data[i] = (byte) (sample & 0xFF);
            data[i+1] = (byte) ((sample >> 8) & 0xFF);
        }
    }
}
