package cosyvoice;

import com.alibaba.dashscope.audio.tts.SpeechSynthesisResult;
import com.alibaba.dashscope.audio.ttsv2.SpeechSynthesisAudioFormat;
import com.alibaba.dashscope.audio.ttsv2.SpeechSynthesisParam;
import com.alibaba.dashscope.audio.ttsv2.SpeechSynthesizer;
import com.alibaba.dashscope.common.ResultCallback;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.CountDownLatch;

public class StreamMain {
    private static String model = "cosyvoice-v2";
    private static String voice = "cosyvoice-v2-prefix-2cd30d23bfda4bdd853d67f5b44246bb";
    private static String[] textArray = {
        "今天的","课程", "主要是中医","按摩手法，",
        "教您应对头脑昏沉、心跳过快", "行动受限、眼前发黑、恶心、呕吐、咳嗽", "等问题。",
        "这个课程很实用的，能帮助您解决不少身体上的小困扰。",
        "课程链接我已经发","到您微信上了，您有空的时候","可以随时点开观看。", 
        "另外，看完课程有", "红包可领，还有额外","完课福利，是石老师", "健康跑视频。",
        "需要注意的是，课程链接当天有效，","明天就无法观看了哦。"
    };


    public static void streamAudioDataToSpeaker() throws FileNotFoundException {
        CountDownLatch latch = new CountDownLatch(1);
        File file = new File("output_stram.pcm");
        final FileOutputStream fos = new FileOutputStream(file);

        // 配置回调函数
        ResultCallback<SpeechSynthesisResult> callback = new ResultCallback<SpeechSynthesisResult>() {
            @Override
            public void onEvent(SpeechSynthesisResult result) {
                // System.out.println("收到消息: " + result);
                if (result.getAudioFrame() != null) {
                    // 此处实现处理音频数据的逻辑
                    System.out.println(TimeUtils.getTimestamp() + " 收到音频");

                    try {
                        fos.write(result.getAudioFrame().array());
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                }
            }

            @Override
            public void onComplete() {
                System.out.println(TimeUtils.getTimestamp() + " 收到Complete，语音合成结束");
                try {
                    fos.close();
                } catch (IOException e) {
                    throw new RuntimeException(e);
                }
                latch.countDown();
            }

            @Override
            public void onError(Exception e) {
                System.out.println("收到错误: " + e.toString());
                latch.countDown();
            }
        };

        // 请求参数
        SpeechSynthesisParam param =
                SpeechSynthesisParam.builder()
                        // 若没有将API Key配置到环境变量中，需将下面这行代码注释放开，并将your-api-key替换为自己的API Key
                         .apiKey("sk-90173df0970e4a9ab9bc6b709a09e358")
                        .model(model)
                        .voice(voice)
                        .format(SpeechSynthesisAudioFormat.PCM_48000HZ_MONO_16BIT) // 流式合成使用PCM或者MP3
                        .build();
        SpeechSynthesizer synthesizer = new SpeechSynthesizer(param, callback);
        // 带Callback的call方法将不会阻塞当前线程
        for (String text : textArray) {
            // 发送文本片段，在回调接口的onEvent方法中实时获取二进制音频
            synthesizer.streamingCall(text);
            try {
                Thread.sleep(100);
            } catch (InterruptedException e1) {
                e1.printStackTrace();
            }
        }
        // 结束流式语音合成
        synthesizer.streamingComplete();
        System.out.println(
                "[Metric] requestId: "
                        + synthesizer.getLastRequestId()
                        + ", first package delay ms: "
                        + synthesizer.getFirstPackageDelay());
        // 等待合成完成
        try {
            latch.await();
            // 等待播放线程全部播放完
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }

        try {
            Thread.sleep(5000);
        } catch (InterruptedException e) {
            throw new RuntimeException(e);
        }
    }

    public static void main(String[] args) {
        try {
            streamAudioDataToSpeaker();
        } catch (FileNotFoundException e) {
            throw new RuntimeException(e);
        }
        System.exit(0);
    }
}