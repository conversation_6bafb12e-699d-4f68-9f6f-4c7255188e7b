package cosyvoice;

import com.alibaba.dashscope.audio.ttsv2.SpeechSynthesisParam;
import com.alibaba.dashscope.audio.ttsv2.SpeechSynthesizer;
import com.alibaba.dashscope.audio.ttsv2.enrollment.Voice;
import com.alibaba.dashscope.audio.ttsv2.enrollment.VoiceEnrollmentService;
import com.alibaba.dashscope.exception.InputRequiredException;
import com.alibaba.dashscope.exception.NoApiKeyException;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import static java.lang.System.exit;

// cosyvoice-v2-prefix-16655e7ea3a940cb977b63d1891a9d17
// cosyvoice-v2-prefix-2cd30d23bfda4bdd853d67f5b44246bb
// cosyvoice-v2-prefix-ddd5c24a8ad54c938846b86571a80537

public class VoiceEnrollmentSampleCodes {
    public static String apiKey = "sk-90173df0970e4a9ab9bc6b709a09e358";
    private static String fileUrl = "https://vediqallmfront.bjmantis.net/111.wav";  // 请按实际情况进行替换
    private static String prefix = "prefix";
    private static String targetModel = "cosyvoice-v2";

    public static void main(String[] args) {
        // 复刻声音
        VoiceEnrollmentService service = new VoiceEnrollmentService(apiKey);
        Voice myVoice = null;
        try {
            myVoice = service.createVoice(targetModel, prefix, fileUrl);
        } catch (NoApiKeyException e) {
            System.out.println(e);
        } catch (InputRequiredException e) {
            System.out.println(e);
        }
        System.out.println("RequestId: " + service.getLastRequestId());
        System.out.println("your voice id is " + myVoice.getVoiceId());



        // 使用复刻的声音来合成文本为语音
        SpeechSynthesisParam param = SpeechSynthesisParam.builder()
                .apiKey(apiKey)
                .model(targetModel)
                .voice(myVoice.getVoiceId())
                .build();
        SpeechSynthesizer synthesizer = new SpeechSynthesizer(param, null);
        ByteBuffer audio = synthesizer.call("今天天气怎么样？");
        // 保存合成的语音到文件
        System.out.println("TTS RequestId: " + synthesizer.getLastRequestId());
        File file = new File("output.mp3");
        try (FileOutputStream fos = new FileOutputStream(file)) {
            fos.write(audio.array());
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        exit(0);
    }
}