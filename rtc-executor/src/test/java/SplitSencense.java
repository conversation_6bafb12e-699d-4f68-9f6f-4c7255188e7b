
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class SplitSencense {

    // 定义用于拆分的标点符号（可按需扩展）
    private static final String SPLIT_PUNCTUATIONS = "[。！？!?；;]";
    // 用于查找拆分位置的标点符号模式（包含更多可能的分隔符，如逗号、冒号等，作为次级选择）
    private static final String PUNCTUATION_PATTERN = "[。！？!?；;,:：，]";
    private static final int MAX_LENGTH = 400; // 最大句长（汉字数）
    private static final int MIN_LENGTH = 10;  // 最小句长（汉字数）

    /**
     * 将文本拆分为句子列表
     *
     * @param text 待拆分的原始文本
     * @return 拆分后的句子列表
     */
    public static List<String> splitTextIntoSentences(String text) {
        List<String> sentences = new ArrayList<>();
        if (text == null || text.trim().isEmpty()) {
            return sentences;
        }

        // 首先按照主要标点符号进行初步拆分
        String[] roughSentences = text.split(SPLIT_PUNCTUATIONS);
        int currentIndex = 0;

        for (String roughSentence : roughSentences) {
            currentIndex += roughSentence.length();
            // 获取当前粗略句子后的标点（保留标点符号）
            if (currentIndex < text.length()) {
                char punctuation = text.charAt(currentIndex);
                roughSentence += punctuation;
                currentIndex++;
            }

            // 处理过长的句子
            if (roughSentence.length() > MAX_LENGTH) {
                sentences.addAll(splitLongSentence(roughSentence));
            } else {
                // 确保句子不少于最小长度（除非本身就是短句）
                if (sentences.isEmpty() ||
                    sentences.get(sentences.size() - 1).length() < MIN_LENGTH &&
                    roughSentence.length() < MIN_LENGTH) {
                    // 合并短句
                    if (!sentences.isEmpty()) {
                        String lastSentence = sentences.remove(sentences.size() - 1);
                        roughSentence = lastSentence + roughSentence;
                    }
                    sentences.add(roughSentence);
                } else {
                    sentences.add(roughSentence);
                }
            }
        }

        return sentences;
    }

    /**
     * 处理超过最大长度的句子
     *
     * @param longSentence 过长的句子
     * @return 拆分后的子句列表
     */
    private static List<String> splitLongSentence(String longSentence) {
        List<String> segments = new ArrayList<>();
        int start = 0;

        while (start < longSentence.length()) {
            int end = Math.min(start + MAX_LENGTH, longSentence.length());

            if (end == longSentence.length()) {
                segments.add(longSentence.substring(start));
                break;
            }

            // 从最大长度处往回找最近的标点符号
            String segment = longSentence.substring(start, end);
            int splitPosition = findBestSplitPosition(segment);

            if (splitPosition == -1 || splitPosition < MIN_LENGTH) {
                // 找不到合适的标点，尝试往前找
                String remainingText = longSentence.substring(start);
                int forwardSearch = findSplitInRemainingText(remainingText);

                if (forwardSearch != -1) {
                    splitPosition = forwardSearch;
                } else {
                    // 实在找不到标点，只能按长度强制分割
                    splitPosition = MAX_LENGTH;
                }
            } else {
                splitPosition += 1; // 包含标点符号
            }

            String chosenSegment = longSentence.substring(start, start + splitPosition).trim();
            if (chosenSegment.length() >= MIN_LENGTH) {
                segments.add(chosenSegment);
            } else if (!segments.isEmpty()) {
                // 如果段落太短，合并到前一段
                String lastSegment = segments.remove(segments.size() - 1);
                segments.add(lastSegment + chosenSegment);
            } else {
                segments.add(chosenSegment);
            }

            start += splitPosition;
        }

        return segments;
    }

    /**
     * 在文本段中查找最佳拆分位置
     *
     * @param segment 待分析的文本段
     * @return 最佳拆分位置（相对于段落的起始位置）
     */
    private static int findBestSplitPosition(String segment) {
        Pattern pattern = Pattern.compile(PUNCTUATION_PATTERN);
        Matcher matcher = pattern.matcher(segment);

        int lastMatchPos = -1;
        while (matcher.find()) {
            lastMatchPos = matcher.start();
        }

        return lastMatchPos;
    }

    /**
     * 在剩余文本中查找拆分位置
     *
     * @param text 剩余文本
     * @return 拆分位置
     */
    private static int findSplitInRemainingText(String text) {
        Pattern pattern = Pattern.compile(PUNCTUATION_PATTERN);
        Matcher matcher = pattern.matcher(text);

        if (matcher.find()) {
            int pos = matcher.start();
            // 确保拆分后的段落不会太短
            if (pos >= MIN_LENGTH || text.length() - pos <= MIN_LENGTH) {
                return pos + 1; // 包含标点符号
            }
        }

        return -1;
    }

    /**
     * 测试用例
     */
    public static void main(String[] args) {
        // 测试用例1：普通文本
        String testText1 = "这是一个简单的句子。这是另一个句子！请问这是第三个句子？最后这是一个稍长的句子；这个句子有点长，超过了最大长度限制，需要被拆分。";
        System.out.println("测试1:");
        splitTextIntoSentences(testText1).forEach(System.out::println);

        System.out.println("\n" + "=".repeat(50) + "\n");

        // 测试用例2：长句子测试
        String testText2 = "这是一段非常长的文本，它没有任何标点符号来帮助我们进行拆分，因此需要程序能够智能地处理这种情况，按照最大长度限制进行拆分，同时尽量保证不会在词语中间断开，从而保持阅读的连贯性和理解性。";
        System.out.println("测试2:");
        splitTextIntoSentences(testText2).forEach(System.out::println);

        System.out.println("\n" + "=".repeat(50) + "\n");

        // 测试用例3：边界情况测试
        String testText3 = "短句。";
        System.out.println("测试3:");
        splitTextIntoSentences(testText3).forEach(System.out::println);
    }
}
