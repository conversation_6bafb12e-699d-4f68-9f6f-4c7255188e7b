package tts.volcan;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;

public class PcmFileProcessor {

    /**
     * 读取 PCM 文件，返回其前一半数据，并将前一半数据写入另一个 PCM 文件
     * @param inputFilePath 输入 PCM 文件的路径
     * @param outputFilePath 输出 PCM 文件的路径
     * @return 输入 PCM 文件前一半数据的字节数组
     * @throws IOException 如果文件读取或写入过程中出现错误
     */
    public static byte[] readAndWriteHalfPcmData(String inputFilePath, String outputFilePath) throws IOException {
        // 创建输入文件对象
        File inputFile = new File(inputFilePath);
        // 创建输入文件流
        try (FileInputStream fis = new FileInputStream(inputFile);
             // 创建输出文件流
             FileOutputStream fos = new FileOutputStream(outputFilePath)) {
            // 获取输入文件的字节长度
            long fileLength = inputFile.length();
            // 计算前一半数据的字节数
            int halfLength = (int) (fileLength / 2);
            // 创建字节数组用于存储前一半数据
            byte[] halfData = new byte[halfLength];
            // 从输入文件流中读取前一半数据到字节数组
            fis.read(halfData);
            // 将前一半数据写入输出文件流
            fos.write(halfData);
            // 返回前一半数据的字节数组
            return halfData;
        }
    }
}