package tts.volcan.unidirection.volcengine;

import com.fasterxml.jackson.databind.ObjectMapper;
import tts.volcan.unidirection.protocol.EventType;
import tts.volcan.unidirection.protocol.Message;
import tts.volcan.unidirection.protocol.MsgType;
import tts.volcan.unidirection.protocol.SpeechWebSocketClient;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.ByteArrayOutputStream;
import java.net.URI;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.Map;
import java.util.UUID;

@Slf4j
public class UnidirectionalStreamDemo {
    private static final String ENDPOINT = "wss://openspeech.bytedance.com/api/v3/tts/unidirectional/stream";
    private static final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * Get resource ID based on voice type
     *
     * @param voice Voice type string
     * @return Corresponding resource ID
     */
    public static String voiceToResourceId(String voice) {
        // Map different voice types to resource IDs based on actual needs
        if (voice.startsWith("S_")) {
            return "volc.megatts.default";
        }
        return "volc.service_type.10029";
    }

    public static void main(String[] args) throws Exception {
        // Configure parameters
        String voice = "S_xMFuNIst1";
        String resourceId = voiceToResourceId(voice);
        String text = "咱们今天讲道医养生相关实用知识，重要又实用，链接已发微信，有空随时看。您学习效率挺高的，学习时遇到任何问题，随时联系我！您先忙，明天见。您学习效率挺高的，学习时遇到任何问题，随时联系我！您先忙，明天见。您学习效率挺高的，学习时遇到任何问题，随时联系我！您先忙，明天见。";
        String encoding = "wav";

        String appId = "2539339545";
        String accessToken = "oNTFrQJWt7Omv9a67rOBWkW2DuJtNscy";

        if (appId == null || accessToken == null) {
            log.error("Please set appId and accessToken system properties");
            System.exit(1);
        }

        // Set request headers
        Map<String, String> headers = Map.of(
                "X-Api-App-Key", appId,
                "X-Api-Access-Key", accessToken,
                "X-Api-Resource-Id", voiceToResourceId(voice),
                "X-Api-Connect-Id", UUID.randomUUID().toString());

        // Create WebSocket client
        SpeechWebSocketClient client = new SpeechWebSocketClient(new URI(ENDPOINT), headers);
        try {
            client.connectBlocking();
            // Prepare request parameters
            Map<String, Object> request = Map.of(
                    "user", Map.of("uid", UUID.randomUUID().toString()),
                    "req_params", Map.of(
                            "speaker", voice,
                            "audio_params", Map.of(
                                    "format", encoding,
                                    "sample_rate", 24000,
                                    "enable_timestamp", true,
                                    "loudness_rate", -50,
                            "speech_rate", 100),
                            // additions requires a JSON string
                            "additions", objectMapper.writeValueAsString(Map.of(
                                    "disable_markdown_filter", false)),
                            "text", text));

            // Send request
            client.sendFullClientMessage(objectMapper.writeValueAsBytes(request));

            // Receive response
            ByteArrayOutputStream audioStream = new ByteArrayOutputStream();
            while (true) {
                Message msg = client.receiveMessage();
                log.info("Received message: {}", msg);

                if (msg.getType() == MsgType.AUDIO_ONLY_SERVER) {
                    if (msg.getPayload() != null) {
                        audioStream.write(msg.getPayload());
                    }
                } else if (msg.getType() == MsgType.ERROR) {
                    throw new RuntimeException("Server returned error: " + new String(msg.getPayload()));
                }

                if (msg.getType() == MsgType.FULL_SERVER_RESPONSE &&
                        msg.getEvent() == EventType.TTS_SENTENCE_END) {
                    String jsonString = new String(msg.getPayload(), StandardCharsets.UTF_8);
                    log.info("Received TTS response sentence end: {}", jsonString);
                    continue;
                }

                if (msg.getType() == MsgType.FULL_SERVER_RESPONSE &&
                        msg.getEvent() == EventType.SESSION_FINISHED) {
                    break;
                }
            }

            if (audioStream.size() == 0) {
                throw new RuntimeException("No audio data received");
            }

            // Save audio file
            String fileName = String.format("%s.%s", voice, encoding);
            Files.write(new File(fileName).toPath(), audioStream.toByteArray());
            log.info("Audio saved to file: {}", fileName);
        } finally {
            client.closeBlocking();
        }
    }
}