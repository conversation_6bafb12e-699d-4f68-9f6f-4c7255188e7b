package tts.volcan;

import java.io.IOException;

public class Main {
    public static void main(String[] args) {
        String inputFilePath = "/Users/<USER>/install/volcan/2222111.pcm";
        String outputFilePath = "/Users/<USER>/install/volcan/2222111-output.pcm";
        try {
            // 调用工具类方法
            byte[] halfData = PcmFileProcessor.readAndWriteHalfPcmData(inputFilePath, outputFilePath);
            System.out.println("前一半数据已成功写入 " + outputFilePath);
        } catch (IOException e) {
            System.err.println("处理 PCM 文件时出现错误: " + e.getMessage());
        }
    }
}