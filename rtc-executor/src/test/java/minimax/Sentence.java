package minimax;

public class Sentence {

    
    public static void main(String[] args) {
        StringBuilder sentenceBuffer = new StringBuilder();
        String sentence = "你好，我是小明，我是一个学生.今天很开心";
        int i = 0;
        while (i < sentence.length()) {
            char ch = sentence.charAt(i);
            sentenceBuffer.append(ch);
            // 如果遇到标点符号，则将缓冲区内容发送给minimax
            if (ch == '，' || ch == '。' || ch == '！' || ch == '？' || ch == ',' || ch == '.' ) {
                String bufferedText = sentenceBuffer.toString();
                sentenceBuffer.setLength(0);
                System.out.println(bufferedText);
            }
            i++;
        }
        // 如果最后还有内容未输出
        if (sentenceBuffer.length() > 0) {
            System.out.println(sentenceBuffer.toString());
        }
    }
}
