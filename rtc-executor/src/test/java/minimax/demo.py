import json

import requests

group_id = "1849807468884922802"
api_key = "***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"


#复刻音频上传
url = f'https://api.minimax.chat/v1/files/upload?GroupId={group_id}'
headers1 = {
    'authority': 'api.minimax.chat',
    'Authorization': f'Bearer {api_key}'
}


data = {
    'purpose': 'voice_clone'
}

files = {
    'file': open('/Users/<USER>/install/volcan/xiaomin_7616_20241129221606-1.mp3', 'rb')
}
response = requests.post(url, headers=headers1, data=data, files=files)
file_id = response.json().get("file").get("file_id")
print(file_id)




# #示例音频上传
# url = f'https://api.minimax.chat/v1/files/upload?GroupId={group_id}'

# headers1 = {
#     'authority': 'api.minimax.chat',
#     'Authorization': f'Bearer {api_key}'
# }

# data = {
#     'purpose': 'prompt_audio'
# }

# files = {
#     'file': open('prompt.mp3', 'rb')
# }
# response = requests.post(url, headers=headers1, data=data, files=files)
# prompt_file_id = response.json().get("file").get("file_id")
# print(prompt_file_id)


#音频复刻
url = f'https://api.minimax.chat/v1/voice_clone?GroupId={group_id}'
payload2 = json.dumps({
  "file_id": file_id,
  "voice_id": "test1234111111222222",
  "text":"喂，您好。你刚刚是在抖音上咨询法律问题了，对吧？",
  "model":"speech-02-hd",
#   "text_validation":"一闪一闪亮晶晶，满天都是小星星。挂在天空放光明，好像许多小眼睛。",
  "accuracy":0.8
#   "clone_prompt":{
#         "prompt_audio":prompt_file_id,
#         "prompt_text":"prompt_audio对应的文本"
#     }
})

headers2 = {
  'Authorization': f'Bearer {api_key}',
  'content-type': 'application/json'
}
response = requests.request("POST", url, headers=headers2, data=payload2)
print(response.text)