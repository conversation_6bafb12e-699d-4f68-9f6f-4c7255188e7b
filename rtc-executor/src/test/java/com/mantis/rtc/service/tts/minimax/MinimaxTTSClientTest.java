package com.mantis.rtc.service.tts.minimax;

import org.junit.Test;

public class MinimaxTTSClientTest {
    
    @Test
    public void testTTS() throws InterruptedException {
        // 替换为你的API密钥
        String apiKey = "your_api_key_here";
        MinimaxTTSClient client = new MinimaxTTSClient(apiKey);
        
        // 连接WebSocket
        client.connect();
        
        // 等待连接建立
        Thread.sleep(1000);
        
        // 开始任务
        client.startTask("这是一个测试");
        
        // 等待任务开始
        Thread.sleep(1000);
        
        // 发送文本
        client.continueTask("这是一个测试文本，用于验证语音合成功能。");
        
        // 等待音频生成
        Thread.sleep(5000);
        
        // 关闭连接
        client.close();
    }
} 