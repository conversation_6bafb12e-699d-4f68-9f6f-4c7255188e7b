package com.mantis.rtc.service.tts.minimax;

import com.google.gson.JsonObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.codec.DecoderException;
import org.apache.commons.codec.binary.Hex;

import java.io.FileOutputStream;
import java.io.IOException;
import java.util.concurrent.TimeUnit;

@Slf4j
public class MinimaxTTSClient {
    private static final String MODULE = "speech-02-hd";
    private static final String EMOTION = "happy";
    private final OkHttpClient client;
    private final String apiKey;
    private WebSocket webSocket;

    public MinimaxTTSClient(String apiKey) {
        this.apiKey = apiKey;
        this.client = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(30, TimeUnit.SECONDS)
                .writeTimeout(30, TimeUnit.SECONDS)
                .pingInterval(30, TimeUnit.SECONDS)
                .build();
    }

    public void connect() {
        Request request = new Request.Builder()
                .url("wss://api.minimax.chat/ws/v1/t2a_v2")
                .header("Authorization", "Bearer " + apiKey)
                .build();

        client.newWebSocket(request, new WebSocketListener() {
            @Override
            public void onOpen(WebSocket webSocket, Response response) {
                log.info("WebSocket连接已建立");
                MinimaxTTSClient.this.webSocket = webSocket;
            }

            @Override
            public void onMessage(WebSocket webSocket, String text) {
                log.info("收到消息: {}", text);
                JsonObject json = com.google.gson.JsonParser.parseString(text).getAsJsonObject();
                String event = json.get("event").getAsString();
                
                if ("connected_success".equals(event)) {
                    log.info("连接成功");
                } else if ("task_started".equals(event)) {
                    log.info("任务已开始");
                } else if (json.has("data") && json.getAsJsonObject("data").has("audio")) {
                    String audioData = json.getAsJsonObject("data").get("audio").getAsString();
                    try {
                        byte[] audioBytes = Hex.decodeHex(audioData);
                        // 保存音频文件
                        try (FileOutputStream fos = new FileOutputStream("output.mp3")) {
                            fos.write(audioBytes);
                        }
                        log.info("音频已保存为output.mp3");
                    } catch (DecoderException | IOException e) {
                        log.error("处理音频数据失败", e);
                    }
                }
            }

            @Override
            public void onClosing(WebSocket webSocket, int code, String reason) {
                log.info("WebSocket正在关闭: code={}, reason={}", code, reason);
            }

            @Override
            public void onClosed(WebSocket webSocket, int code, String reason) {
                log.info("WebSocket已关闭: code={}, reason={}", code, reason);
            }

            @Override
            public void onFailure(WebSocket webSocket, Throwable t, Response response) {
                log.error("WebSocket连接失败", t);
            }
        });
    }

    public void startTask(String text) {
        if (webSocket == null) {
            log.error("WebSocket未连接");
            return;
        }

        JsonObject request = new JsonObject();
        request.addProperty("event", "task_start");
        request.addProperty("model", MODULE);
        
        JsonObject voiceSetting = new JsonObject();
        voiceSetting.addProperty("voice_id", "male-qn-qingse");
        voiceSetting.addProperty("speed", 1);
        voiceSetting.addProperty("vol", 1);
        voiceSetting.addProperty("pitch", 0);
        voiceSetting.addProperty("emotion", EMOTION);
        
        JsonObject audioSetting = new JsonObject();
        audioSetting.addProperty("sample_rate", 32000);
        audioSetting.addProperty("bitrate", 128000);
        audioSetting.addProperty("format", "mp3");
        audioSetting.addProperty("channel", 1);
        
        request.add("voice_setting", voiceSetting);
        request.add("audio_setting", audioSetting);
        
        webSocket.send(request.toString());
    }

    public void continueTask(String text) {
        if (webSocket == null) {
            log.error("WebSocket未连接");
            return;
        }

        JsonObject request = new JsonObject();
        request.addProperty("event", "task_continue");
        request.addProperty("text", text);
        
        webSocket.send(request.toString());
    }

    public void close() {
        if (webSocket != null) {
            JsonObject request = new JsonObject();
            request.addProperty("event", "task_finish");
            webSocket.send(request.toString());
            webSocket.close(1000, "正常关闭");
        }
    }
} 