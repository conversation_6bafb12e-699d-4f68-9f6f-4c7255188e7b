/*
 * Copyright (c) 2010-2020 <PERSON>
 *
 *  Permission is hereby granted, free of charge, to any person
 *  obtaining a copy of this software and associated documentation
 *  files (the "Software"), to deal in the Software without
 *  restriction, including without limitation the rights to use,
 *  copy, modify, merge, publish, distribute, sublicense, and/or sell
 *  copies of the Software, and to permit persons to whom the
 *  Software is furnished to do so, subject to the following
 *  conditions:
 *
 *  The above copyright notice and this permission notice shall be
 *  included in all copies or substantial portions of the Software.
 *
 *  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 *  EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES
 *  OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 *  NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT
 *  HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY,
 *  WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 *  FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR
 *  OTHER DEALINGS IN THE SOFTWARE.
 */

import org.java_websocket.client.WebSocketClient;
import org.java_websocket.drafts.Draft;
import org.java_websocket.handshake.ServerHandshake;

import java.io.*;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Map;

/**
 * This example demonstrates how to create a websocket connection to a server. Only the most
 * important callbacks are overloaded.
 */
public class ExampleClient extends WebSocketClient {

  public ExampleClient(URI serverUri, Draft draft) {
    super(serverUri, draft);
  }

  public ExampleClient(URI serverURI) {
    super(serverURI);
  }

  public ExampleClient(URI serverUri, Map<String, String> httpHeaders) {
    super(serverUri, httpHeaders);
  }

  @Override
  public void onOpen(ServerHandshake handshakedata) {
    System.out.println("opened connection");

    final int sampleRate = 8000;
    // 10毫秒的采样数据长度
    final int frameLength = sampleRate * 10/1000;
    final int channels = 1;
    final byte[] buffer = new byte[frameLength * channels * 2];

    InputStream inputStream = null;
    try {
      inputStream = new FileInputStream("/Users/<USER>/mantis_codes/aigc/micro-rtc-service/rtc-executor/src/test/java/test_submit-8k.pcm");
    } catch (FileNotFoundException e) {
      throw new RuntimeException(e);
    }

    try (InputStream in = new BufferedInputStream(inputStream)) {
      int len;
      while ((len = in.read(buffer)) > 0) {
        if (len != buffer.length) {
          break;
        }
        send(buffer);
        // 10毫秒发送一次数据
        try {
          Thread.sleep(10);
        } catch (InterruptedException e) {
          throw new RuntimeException(e);
        }
      }

    } catch (IOException e) {
      throw new RuntimeException(e);
    }
  }

  @Override
  public void onMessage(String message) {
    System.out.println("received: " + message);
  }

  @Override
  public void onClose(int code, String reason, boolean remote) {
    // The close codes are documented in class org.java_websocket.framing.CloseFrame
    System.out.println(
        "Connection closed by " + (remote ? "remote peer" : "us") + " Code: " + code + " Reason: "
            + reason);
  }

  @Override
  public void onError(Exception ex) {
    ex.printStackTrace();
    // if the error is fatal then onClose will be called additionally
  }

  public static void main(String[] args) throws URISyntaxException {
    ExampleClient c = new ExampleClient(new URI(
        "ws://115.190.14.27:2345")); // more about drafts here: http://github.com/TooTallNate/Java-WebSocket/wiki/Drafts
    c.connect();
  }

}