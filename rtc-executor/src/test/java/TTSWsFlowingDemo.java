/**
 * 本接口服务采用 websocket 协议，将请求文本合成为音频，同步返回合成音频数据及相关文本信息，达到“边合成边播放”的效果。
 * 在使用该接口前，需要 开通语音合成服务，并进入 API 密钥管理页面 新建密钥，生成 AppID、SecretID 和 SecretKey，用于 API 调用时生成签名，签名将用来进行接口鉴权。
 */
public class TTSWsFlowingDemo {
//    static Logger logger = LoggerFactory.getLogger(TTSWsFlowingDemo.class);
//
//    //SpeechClient应用全局创建一个即可,生命周期可和整个应用保持一致
//    static SpeechClient proxy = new SpeechClient(TtsConstant.DEFAULT_TTS_V2_REQ_URL);
//    private static long currentTimeMillis = 0;
//
//    public static void main(String[] args) throws IOException {
//        //在腾讯云控制台[账号信息](https://console.cloud.tencent.com/developer)页面查看账号APPID，[访问管理](https://console.cloud.tencent.com/cam/capi)页面获取 SecretID 和 SecretKey 。
//        //todo 在使用该接口前，需要开通该服务，并请将下面appId、secretId、secretKey替换为自己账号信息。
//        String appId = "1315816490";
//        String secretId = "AKIDraAoOoHepoqmaIawDLGK6wgQX2MTO5as";
//        String secretKey = "82jDcuNly0dAh3C8uVuEUZXPAnFXdXJt";
//        process(appId, secretId, secretKey);
//        proxy.shutdown();
//    }
//
//    public static void process(String appId, String secretId, String secretKey) {
//        Credential credential = new Credential(appId, secretId, secretKey);
//        FlowingSpeechSynthesizerRequest request = new FlowingSpeechSynthesizerRequest();
//        request.setVolume(0f);
//        request.setSpeed(0f);
//        request.setCodec("mp3");
//        request.setSampleRate(16000);
//        request.setVoiceType(200000000);
//        request.setEnableSubtitle(true);
//        request.setEmotionCategory("call");
//        request.setEmotionIntensity(100);
//        request.setSessionId(UUID.randomUUID().toString());//sessionId，需要保持全局唯一（推荐使用 uuid），遇到问题需要提供该值方便服务端排查
//
//        Map<String, Object> ext=new HashMap<>();
//        ext.put("FastVoiceType", "WCHN-d03d100f090c467e941d387aa82621e3");
//        request.setExtendParam(ext);
//        //request.set("SegmentRate", 1); //sdk暂未支持参数，可通过该方法设置
//        logger.debug("session_id:{}", request.getSessionId());
//        FlowingSpeechSynthesizerListener listener = new FlowingSpeechSynthesizerListener() {//tips：回调方法中应该避免进行耗时操作，如果有耗时操作建议进行异步处理否则会影响websocket请求处理
//            byte[] audio = new byte[0];
//
//
//            @Override
//            public void onSynthesisStart(SpeechSynthesizerResponse response) {
//                logger.info("{} session_id:{},{}", "onSynthesisStart", response.getSessionId(), new Gson().toJson(response));
//            }
//
//            @Override
//            public void onSynthesisEnd(SpeechSynthesizerResponse response) {
//                logger.info("{} session_id:{},{}", "onSynthesisEnd", response.getSessionId(), new Gson().toJson(response));
//                if ("pcm".equals(request.getCodec())) {
//                    Ttsutils.responsePcm2Wav(16000, audio, request.getSessionId());
//                }
//                if ("mp3".equals(request.getCodec())) {
//                    try {
//                        FileOutputStream out = new FileOutputStream("test301021.mp3", false);
//                        out.write(audio);
//                        out.close();
//                    } catch (IOException e) {
//                        e.printStackTrace();
//                    }
//                }
//            }
//
//            @Override
//            public void onAudioResult(ByteBuffer buffer) {
//                logger.info("perf {} ms, thead {}" , System.currentTimeMillis() - currentTimeMillis, Thread.currentThread().getName());
//                byte[] data = new byte[buffer.remaining()];
//                buffer.get(data);
//                audio = ByteUtils.concat(audio, data);
//            }
//
//            @Override
//            public void onTextResult(SpeechSynthesizerResponse response) {
//                logger.info("{} session_id:{},{}", "onTextResult", response.getSessionId(), new Gson().toJson(response));
//            }
//
//            /**
//             * 错误回调 当发生错误时回调该方法
//             * @param response 响应
//             */
//            @Override
//            public void onSynthesisFail(SpeechSynthesizerResponse response) {
//                logger.info("{} session_id:{},{}", "onSynthesisFail", response.getSessionId(), new Gson().toJson(response));
//            }
//        };
//        //synthesizer不可重复使用，每次合成需要重新生成新对象
//        FlowingSpeechSynthesizer synthesizer = null;
//        String[] texts = {
//                "你好，",
//                "我是小杨群主；",
//                "我看咱们",
//                "今天的课程",
//                "你是不是还没来学习呢；",
//                "今天的课啊讲了",
//                "很多关于基础方儿",
//                "的重点，你不",
//                "忙的话一定来听一下；",
//                "然后有空的话尽量做一下笔记；",
//                "好吧；行，那你不忙的时候",
//                "记得学习哈，拜拜。"
//        };
//        try {
//            synthesizer = new FlowingSpeechSynthesizer(proxy, credential, request, listener);
//            currentTimeMillis = System.currentTimeMillis();
//            synthesizer.start();
//            logger.info("synthesizer start latency : " + (System.currentTimeMillis() - currentTimeMillis) + " ms");
//            for (String text : texts) {
//                synthesizer.process(text);
//                Thread.sleep(500);
//            }
//            currentTimeMillis = System.currentTimeMillis();
//            synthesizer.stop();
//            logger.info("synthesizer stop latency : " + (System.currentTimeMillis() - currentTimeMillis) + " ms");
//        } catch (Exception e) {
//            logger.error(e.getMessage());
//        } finally {
//            if (synthesizer != null) {
//                synthesizer.close(); //关闭连接
//            }
//        }
//    }
}