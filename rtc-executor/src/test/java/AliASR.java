//import com.alibaba.dashscope.audio.asr.recognition.Recognition;
//import com.alibaba.dashscope.audio.asr.recognition.RecognitionParam;
//import com.alibaba.dashscope.exception.NoApiKeyException;
//import io.reactivex.BackpressureStrategy;
//import io.reactivex.Emitter;
//import io.reactivex.Flowable;
//import lombok.extern.slf4j.Slf4j;
//
//import java.nio.ByteBuffer;

public class AliASR {
//    private static String ALI_API_KEY = "sk-90173df0970e4a9ab9bc6b709a09e358";
//
//    private ByteBuffer audioFrame = ByteBuffer.allocate(1024);
//    private String asr_res = new String();
//    private StringBuffer asr_fix = new StringBuffer();
//
//    public AliASR(){
//        Flowable<ByteBuffer> audioSource =
//                Flowable.create(
//                        emitter -> {
//                            new Thread(() -> {
//                                try{
//                                    sendAudio(emitter);
//                                }catch (Exception e){
//                                    log.error("AliASR Flowable create ERROR:",e);
//                                    emitter.onError(e);
//                                }
//                            }).start();
//                        },
//                        BackpressureStrategy.BUFFER);
//
//        // 流式调用接口
//        new Thread(() -> {
//            blockingForEach(audioSource);
//        }).start();
//    }
//
//    private void sendAudio(Emitter emitter){
//        try{
//            ByteBuffer buffer = ByteBuffer.allocate(1024);
//            // 录音30s并进行实时转写
//            while (true) {
//                if (!audioFrame.equals(buffer)) {
//                    // 将录音音频数据发送给流式识别服务
//                    emitter.onNext(audioFrame);
//                    audioFrame = ByteBuffer.allocate(1024);
//                    // 结束转写
//                    // emitter.onComplete();
//                }
//            }
//        }catch (Exception e){
//            log.error("AliASR sendAudio ERROR:",e);
////            sendAudio(emitter);
//        }
//    }
//
//    private void blockingForEach(Flowable<ByteBuffer> audioSource) {
//        try {
//            // 创建Recognizer
//            Recognition recognizer = new Recognition();
//            // 创建RecognitionParam，audioFrames参数中传入上面创建的Flowable<ByteBuffer>
//            RecognitionParam param =
//                    RecognitionParam.builder()
//                            .model("paraformer-realtime-v2")
//                            .format("pcm")
//                            .parameter("semantic_punctuation_enabled",false)
//                            .sampleRate(16000)
//                            .disfluencyRemovalEnabled(true)// 过滤语气词，默认关闭
//                            .apiKey(ALI_API_KEY)
//                            .build();
//
//            // 调用Flowable的subscribe方法订阅结果
//            recognizer.streamCall(param, audioSource).blockingForEach(
//                    result -> {
//                        // 打印最终结果
//                        if (result.isSentenceEnd()) {
//                            String text = result.getSentence().getText();
//                            log.info("AliASR ------ FIX:{}",text);
//                            asr_fix.append(text);
//                            asr_res = new String();
//                        }else {
//                            String text = result.getSentence().getText();
//                            log.info("AliASR ------ Result:{}", text);
//                            asr_res = text;
//                        }
//                    });
//        } catch (NoApiKeyException e) {
//            log.error("AliASR blockingForEach ERROR:",e);
////            blockingForEach(startTime, audioSource);
//        }
//    }
//
//    public void setAudioFrame(ByteBuffer audioFrame) {
//        this.audioFrame = audioFrame;
//    }
//
//    public String getAsr_res() {
//        String astResStr = asr_fix.toString().concat(asr_res);
//        asr_fix = new StringBuffer();
//        asr_res = "";
//        return astResStr;
//    }

}
