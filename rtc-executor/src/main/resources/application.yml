logging:
  level:
    root: info
    com.mantis: info
    com.mantis.rtc.service.rtc: info
    com.mantis.rtc.service.llm: info
    com.mantis.rtc.service.asr.volcan: info
    com.mantis.rtc.service.gateway.InfoReportService: info

server:
  tomcat:
    connection-timeout: 5000
    threads:
      max: 5
      min-spare: 2

spring:
  profiles:
    active: @activatedProperties@j
  cloud:
    nacos:
      discovery:
        register-enabled: false
  application:
      name: rtc-executor

mantis:
  service:
    url: https://vedIqartcgateway.bjmantis.net
  vad:
    record: false
    allowInterrupt: true
  llm:
    volcan:
      apiKey: 91d93cca-0125-4e00-b5c4-8869e0ea9536
      url: https://ark.cn-beijing.volces.com/api/v3
    deepseek:
      apiKey: ***********************************
      url: https://api.deepseek.com
    tencent:
      apiKey: Bearer sk-wi9wsZyHzJ6b6bSqdBu2gJ0q7CBzeoyJTKhJTglg3JUct6o1
      url: https://api.lkeap.cloud.tencent.com/v1
    theturbo:
      apiKey: sk-qw7eXcd7mS7v9SKHgz6zt4gfD4UGcCYlZlvYhgxc4yI7HpJ5
      url: https://gateway.theturbo.ai/v1