<?xml version="1.0" encoding="UTF-8"?>

<configuration scan="true" scanPeriod="120 seconds" debug="false">

    <springProperty scope="context" name="sls_topic" source="spring.application.name" />
    <springProperty scope="context" name="sls_env" source="sls.evnCode" />
    <springProperty scope="context" name="sls_port" source="server.port" />
    <springProperty scope="context" name="mantisLogAppender" source="sls.appender" defaultValue="aliyun"/>

    <springProperty scope="context" name="ali_endpoint" source="sls.aliyun.endpoint" />
    <springProperty scope="context" name="ali_accessKeyId" source="sls.aliyun.accessKeyId" />
    <springProperty scope="context" name="ali_accessKeySecret" source="sls.aliyun.accessKeySecret" />
    <springProperty scope="context" name="ali_project" source="sls.aliyun.project" />
    <springProperty scope="context" name="ali_logStore" source="sls.aliyun.logStore" />

    <shutdownHook class="ch.qos.logback.core.hook.DelayingShutdownHook"/>

    <springProfile name="dev">
        <substitutionProperty name="log.base" value="${catalina.base}/logs"/>
    </springProfile>

    <appender name="stdout" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>[%date][%thread][%logger] %msg%n</pattern>
        </encoder>
    </appender>

    <!-- aliyun appender -->
    <appender name="aliyun" class="com.aliyun.openservices.log.logback.LoghubAppender">
        <!--必选项-->
        <!-- 账号及网络配置 -->
        <endpoint>${ali_endpoint}</endpoint>
        <accessKeyId>${ali_accessKeyId}</accessKeyId>
        <accessKeySecret>${ali_accessKeySecret}</accessKeySecret>

        <!-- sls 项目配置 -->
        <project>${ali_project}</project>
        <logStore>${ali_logStore}</logStore>
        <!--必选项 (end)-->

        <topic>${sls_topic}-${sls_env}</topic>

        <!-- 可选项 详见 '参数说明'-->
        <totalSizeInBytes>104857600</totalSizeInBytes>
        <maxBlockMs>0</maxBlockMs>
        <ioThreadCount>4</ioThreadCount>
        <batchSizeThresholdInBytes>524288</batchSizeThresholdInBytes>
        <batchCountThreshold>4096</batchCountThreshold>
        <lingerMs>2000</lingerMs>
        <retries>10</retries>
        <baseRetryBackoffMs>100</baseRetryBackoffMs>
        <maxRetryBackoffMs>50000</maxRetryBackoffMs>

        <!-- 可选项 通过配置 encoder 的 pattern 自定义 log 的格式 -->
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <pattern>[%logger][%thread] %msg%n</pattern>
        </encoder>

        <!-- 可选项 设置 time 字段呈现的格式 -->
        <timeFormat>yyyy-MM-dd HH:mm:ss.SSS</timeFormat>
        <!-- 可选项 设置 time 字段呈现的时区 -->
        <timeZone>Asia/Shanghai</timeZone>
        <!-- 可选项 设置是否要添加 Location 字段（日志打印位置），默认为 true -->
        <includeLocation>false</includeLocation>
        <!-- 可选项 当 encoder 不为空时，是否要包含 message 字段，默认为 true -->
        <includeMessage>false</includeMessage>
        <!-- in the format of "KEY1,KEY2,KEY3" or "*" to match all keys -->
        <mdcFields>*</mdcFields>
    </appender>

    <logger name="com.mantis" level="info"/>
    <logger name="org.springframework" level="INFO"/>
    <logger name="org.apache.commons" level="INFO"/>
    <logger name="com.mantis.rtc.service.llm" level="info"/>
    <logger name="com.mantis.rtc.service.tts.volcan" level="info"/>
    <logger name="org.apache.http" level="ERROR"/>
    <logger name="com.alibaba.nacos.client.config.impl" level="ERROR"/>
    <logger name="reactor.io.net.impl.netty" level="INFO"/>
    <logger name="com.netflix.discovery.shared.NamedConnectionPool" level="ERROR"/>
    <logger name="com.netflix.discovery.shared.MonitoredConnectionManager" level="ERROR"/>

    <root>
        <level value="info"/>
<!--        <appender-ref ref="stdout"/>-->
        <appender-ref ref="${mantisLogAppender}"/>
    </root>
</configuration>