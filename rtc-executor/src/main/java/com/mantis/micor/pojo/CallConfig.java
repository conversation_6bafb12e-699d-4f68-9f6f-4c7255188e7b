package com.mantis.micor.pojo;

import lombok.Data;

import java.util.List;

/**
 * 呼叫配置
 * @date 2022年1月19日14:30:53
 */
@Data
public class CallConfig {
    /**
     * 呼叫渠道，tel, rtc
     */
    private String channel;
    /**
     * vad 配置参数
     */
    private Vad vad;
    /**
     * tel 呼叫的配置
     */
    private Tel tel;
    /**
     * rtc 配置
     */
    private RTC rtc;

    /**
     * agora rtc 配置
     */
    private AgoraRTC agoraRtc;

    private ASR asr;

    /**
     * 语音合成配置
     */
    private TTS tts;

    /**
     * 大模型配置
     */
    private LLM llm;

    private Knowledge knowledge;

    /**
     * 是否允许打断
     */
    private Boolean allowInterrupt = true;

    /**
     * 欢迎语
     */
    private String welcomeSpeech;
    /**
     *
     * 自定义配置
     */
    private String extra;

    /**
     * 字幕回调配置
     */
    private SubtitleCallBackConfig subtitleCallBackConfig;

    private BizCallBack bizCallBack;

    @Data
    public static class Vad{
        /**
         * 最小静音时间，单位：毫秒
         */
        private int min_silence_time = 1300;
        /**
         * 最小说话时间，单位：毫秒
         */
        private int min_speech_time = 1200;
        /**
         * 唤醒时间，单位：毫秒
         */
        private int wake_up_time = 5000;
        /**
         * 最大唤醒次数
         */
        private int max_wake_up_count = 3;
        /**
         * 是否在达到最大唤醒次数后挂断
         */
        private boolean hangup_after_max_wake_up = false;
    }

    @Data
    public static class Tel{
        private String customerId;
        private String prefix;
        private String called;
        private String gateway;
    }

    @Data
    public static class RTC{
        /**
         * 学员id
         */
        private String targetUserId;
        /**
         * rtc appId
         */
        private String appId;

        /**
         * rtc appKey
         */
        private String appKey;
        private Float volumeGain = 0.3f;
    }

    @Data
    public static class AgoraRTC{
        /**
         * 学员id
         */
        private String targetUserId;

        private Integer targetUserIdMobile;

        /**
         * rtc appId
         */
        private String appId;
        /**
         * rtc appKey
         */
        private String appKey;
         /**
         * 声网证书
         */
        private String appCertificate;
        
        private Float volumeGain = 1f;
    }

    @Data
    public static class TTS{
        // volcan, cosyvoice
        private String vendor;
        private VolcanTTS volcan;
        private CosyvoiceTTS cosyvoice;
        private MinimaxTTS minimax;
    }

    @Data
    public static class ASR{
        // volcan, paraformer, tencent
        private String vendor;
        private TencentAsr tencent;
        private VolcanAsr volcan;
        private ParaformerAsr paraformer;
    }

    @Data
    public static class TencentAsr {
        private String appId;
        private String secretId;
        private String secretKey;
    }

    @Data
    public static class VolcanAsr {
        private String appId;
        private String appKey;
    }

    @Data
    public static class ParaformerAsr{
        private String apiKey;
        private String model;
    }


    @Data
    public static class VolcanTTS{
        /**
         * 火山appId
         */
        private String appId;
        private String appKey;
        private String cluster;
        private String voiceId;
        /**
         * 语速，取值范围[-50,100]，100代表2.0倍速，-50代表0.5倍数
         */
        private Integer speed = 0;
        private Integer loudness = -50;
    }

    @Data
    public static class CosyvoiceTTS{
        private String appkey;
        private String model;
        private String voiceId;
        private float speed = 1;
    }

    @Data
    public static class MinimaxTTS {
        private String apiKey;
        private String groupId;
        private String voiceId;
        private String model;
    }

    @Data
    public static class Knowledge{
        // tencent, volcan
        private String vendor;
        private VolcanKn volcan;
        private TencentKn tencent;
        //过滤条件
        private  KnFilter filter;
    }

    @Data
    public static class KnFilter{
        private List<String> tags;
    }

    @Data
    public static class TencentKn{
        private String secretId;
        private String secretKey;
        private String knowledgeBaseId;
        private float scoreLimit;
    }

    @Data
    public static class VolcanKn{
        private String ak;
        private String sk;
        private String name;
        private float scoreLimit;
    }

    @Data
    public static class LLM{
        private String vendor = "volcan";
        private String systemPrompt;
        private String userPrompt;
        private String model;
        private String apiKey;
        private String url;
        private Float temperature = 1.0f;
        private Float frequencyPenalty;
        private Boolean response = false;
        /**
         * 是否使用大模型cache
         */
        private Boolean useCache = true;
        /**
         * 历史问题长度，不包含预设System角色、User角色内容。取值范围为 [1,20)，默认值为 3。
         */
        private Integer historyLength = 30;
    }

    @Data
    public static class SubtitleCallBackConfig{
        private String serverMessageUrl;
    }

    @Data
    public static class BizCallBack{
        private String serverMessageUrl;
    }
}
