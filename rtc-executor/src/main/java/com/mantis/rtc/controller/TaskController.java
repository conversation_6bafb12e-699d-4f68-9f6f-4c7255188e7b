package com.mantis.rtc.controller;

import com.alibaba.fastjson.JSON;
// import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONObject;
import com.mantis.micor.http.HttpResult;
import com.mantis.micor.http.HttpResultConstant;
import com.mantis.micor.pojo.CallConfig;
import com.mantis.micor.pojo.CallTask;
import com.mantis.rtc.pojo.Constants;
import com.mantis.rtc.service.CallContextService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import static com.mantis.rtc.service.llm.LLMService.LLM_VOLCAN;

// import static com.mantis.rtc.pojo.Constants.TTS_VENDOR_COSYVOICE;
// import static com.mantis.rtc.pojo.Constants.TTS_VENDOR_VOLCAN;


@RestController
@RequestMapping("/task")
@Slf4j
public class TaskController {

    @Autowired
    private CallContextService contextService;

    /**
     * 接收呼叫请求
     * @param task
     * @return
     */
    @RequestMapping("/init")
    @ResponseBody
    public HttpResult initTask(@RequestBody CallTask task) {
        log.info("start call: {}, executor_node {}, executor_port {}",JSON.toJSONString(task), contextService.getExecutorInfo().getIp(), contextService.getExecutorInfo().getServerPort());

        String roomId = task.getRoomId();
        if(StringUtils.isBlank(roomId)) {
            log.error("invalid roomId, para {}", task);
            return HttpResult.error(HttpResultConstant.FAIL_CODE,"invalid roomId");
        }

        Integer companyId = task.getCompanyId();
        if(companyId == null) {
            log.error("invalid companyId, para {}", task);
            return HttpResult.error(HttpResultConstant.FAIL_CODE,"invalid companyId");
        }

        CallConfig callConfig = task.getCallConfig();
        if(callConfig == null) {
            log.error("invalid callConfig, para {}", task);
            return HttpResult.error(HttpResultConstant.FAIL_CODE,"invalid callConfig");
        }

        CallConfig.ASR asr = task.getCallConfig().getAsr();
        if(asr == null) {
            log.error("invalid asr, please check request, skip init");
            return HttpResult.error(HttpResultConstant.FAIL_CODE,"invalid asr config, asr must");
        }

        String asrVendor = asr.getVendor();
        if(StringUtils.isBlank(asrVendor)) {
            log.error("invalid asr config, para {}", task);
            return HttpResult.error(HttpResultConstant.FAIL_CODE,"invalid asr config, asr vendor is must");
        }

        if("volcan".equalsIgnoreCase(asrVendor)) {
            if(asr.getVolcan() == null || StringUtils.isBlank(asr.getVolcan().getAppId()) || StringUtils.isBlank(asr.getVolcan().getAppKey()) ) {
                log.error("invalid asr config, para {}", asr);
                return HttpResult.error(HttpResultConstant.FAIL_CODE,"invalid volcan asr config");
            }
        }
        if("paraformer".equalsIgnoreCase(asrVendor)) {
            if(asr.getParaformer() == null || StringUtils.isBlank(asr.getParaformer().getApiKey()) || StringUtils.isBlank(asr.getParaformer().getModel())) {
                log.error("invalid paraformer asr config, para {}", task);
                return HttpResult.error(HttpResultConstant.FAIL_CODE,"invalid paraformer asr config");
            }
        }

        CallConfig.TTS tts = task.getCallConfig().getTts();
        if(tts == null) {
            log.error("invalid tts config, para {}", task);
            return HttpResult.error(HttpResultConstant.FAIL_CODE,"invalid tts config");
        }

        String vendor = tts.getVendor();
        if(StringUtils.isBlank(vendor)) {
            log.error("invalid tts config, para {}", task);
            return HttpResult.error(HttpResultConstant.FAIL_CODE,"invalid tts config, tts vendor is must");
        }

        if(!vendor.equalsIgnoreCase(Constants.TTS_VENDOR_VOLCAN) && !vendor.equalsIgnoreCase(Constants.TTS_VENDOR_COSYVOICE) && !vendor.equalsIgnoreCase(Constants.TTS_VENDOR_MINIMAX)) {
            log.error("invalid tts config, para {}", task);
            return HttpResult.error(HttpResultConstant.FAIL_CODE,"invalid tts config, tts vendor is invalid");
        }

        if(Constants.TTS_VENDOR_VOLCAN.equalsIgnoreCase(vendor)) {
            if(tts.getVolcan() == null || StringUtils.isBlank(tts.getVolcan().getAppId()) || StringUtils.isBlank(tts.getVolcan().getAppKey()) || StringUtils.isBlank(tts.getVolcan().getVoiceId())) {
                log.error("invalid volcan tts config, para {}", tts);
                return HttpResult.error(HttpResultConstant.FAIL_CODE,"invalid volcan tts config");
            }
        }

        if(Constants.TTS_VENDOR_COSYVOICE.equalsIgnoreCase(vendor)) {
            if(tts.getCosyvoice() == null || StringUtils.isBlank(tts.getCosyvoice().getAppkey()) || StringUtils.isBlank(tts.getCosyvoice().getVoiceId())) {
                log.error("invalid cosyvoice tts config, para {}", task);
                return HttpResult.error(HttpResultConstant.FAIL_CODE,"invalid cosyVoice tts config");
            }
        }

        if(Constants.TTS_VENDOR_MINIMAX.equalsIgnoreCase(vendor)) {
            if(tts.getMinimax() == null || StringUtils.isBlank(tts.getMinimax().getApiKey()) || StringUtils.isBlank(tts.getMinimax().getVoiceId()) || StringUtils.isBlank(tts.getMinimax().getModel()) || StringUtils.isBlank(tts.getMinimax().getGroupId())) {
                log.error("invalid minimax tts config, para {}", task);
                return HttpResult.error(HttpResultConstant.FAIL_CODE,"invalid minimax tts config");
            }
        }
        
        String systemPrompt = task.getCallConfig().getLlm().getSystemPrompt();
        if(StringUtils.isBlank(systemPrompt)) {
            log.error("invalid llm config, para {}", task);
            return HttpResult.error(HttpResultConstant.FAIL_CODE,"invalid llm config, llm system prompt is empty");
        }

        if (StringUtils.isNotBlank(task.getCallConfig().getExtra())) {
            try {
                task.setExtra(JSON.parseObject(task.getCallConfig().getExtra(), JSONObject.class));
            } catch (Exception e) {
                log.error("fail to parse extra parameter, roomId {}, extra {}", task.getRoomId(), task.getCallConfig().getExtra(), e);
                log.error("invalid extra param, para {}", task);
                return HttpResult.error(HttpResultConstant.FAIL_CODE, "invalid extra param");
            }
        }

        // 清除知识库调用
        if(systemPrompt.contains("版本") && systemPrompt.contains("v2")) {
            task.getCallConfig().setKnowledge(null);
        }

        // 调试参数，llm厂商
        String llmVendor = task.getCallConfig().getLlm().getVendor();
        if(StringUtils.isBlank(llmVendor)) {
            task.getCallConfig().getLlm().setVendor(LLM_VOLCAN);
        }

        String model = task.getCallConfig().getLlm().getModel();
        // todo 调试参数，是否使用response API
        if("ep-20250612153254-8pj68".equalsIgnoreCase(model) || "ep-20250829120149-gr7n7".equalsIgnoreCase(model)) {
            task.getCallConfig().getLlm().setResponse(true);
        } else {
            if(task.getCallConfig().getLlm().getResponse() == null) {
                task.getCallConfig().getLlm().setResponse(false);
            }
        }

        boolean result = contextService.startCall(task);
        if(!result) {
            log.error("invalid init task, para {}", task);
            return HttpResult.error(HttpResultConstant.FAIL_CODE,"fail to init task");
        }

        // 回调准备完毕
        contextService.getInfoReportService().reportStatus(contextService.getTask().getRoomId(), "ready", "准备完毕");
        return HttpResult.ok();
    }

    /**
     * 强制停止呼叫
     * @return
     */
    @RequestMapping("/clean/{roomId}")
    public HttpResult stopVoiceChat(@PathVariable String roomId) {
        log.info("got clean request, begin clean, roomId {}", roomId);
        contextService.clean(roomId);
        return HttpResult.ok();
    }
}
