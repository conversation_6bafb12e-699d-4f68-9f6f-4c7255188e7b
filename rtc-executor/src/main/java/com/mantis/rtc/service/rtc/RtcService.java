package com.mantis.rtc.service.rtc;


import com.mantis.micor.pojo.CallTask;
import com.mantis.rtc.service.CallContextService;
import com.mantis.rtc.service.TaskService;
import com.mantis.rtc.service.rtc.utils.RtcConstants;
import com.mantis.rtc.service.rtc.utils.VolcanoUtil;
import com.mantis.rtc.utils.Utils;
import com.ss.bytertc.engine.RTCRoom;
import com.ss.bytertc.engine.RTCRoomConfig;
import com.ss.bytertc.engine.RTCVideo;
import com.ss.bytertc.engine.UserInfo;
import com.ss.bytertc.engine.data.*;
import com.ss.bytertc.engine.handler.IAudioFrameProcessor;
import com.ss.bytertc.engine.handler.IRTCRoomEventHandler;
import com.ss.bytertc.engine.type.ChannelProfile;
import com.ss.bytertc.engine.utils.AudioFrame;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.function.Consumer;

@Slf4j
@Service
public class RtcService implements TaskService {

    @Autowired
    private CallContextService contextService;

    // ------ --- --- --- --- --- ---  实例变量
    private RTCVideo mRTCVideo = null;
    private RTCRoom mRTCRoom = null;
    private String roomId = "";
    private String appId = "";
    private String appKey = "";
    private String targetUserId = "";


    /**
     * 提前初始化相关资源，以保证发起呼叫时可以快速响应
     * 需要初始化的资源包括：
     * 0. rtc的初始化
     * 1. vad
     * 2. asr
     * 3. tts
     * 4. 大模型
     */
    public boolean init(CallTask task) {
        if(!contextService.isRtcChannel(task)) {
            return true;
        }

        long l = System.currentTimeMillis();
        roomId = task.getRoomId();
        appId = task.getCallConfig().getRtc().getAppId();
        appKey = task.getCallConfig().getRtc().getAppKey();
        targetUserId = task.getCallConfig().getRtc().getTargetUserId();

        log.info("init rtc service begin, appId: {}, roomId {}", appId, task.getRoomId());

        IAudioFrameProcessor audioFrameProcessor = new AudioFrameProcessor(contextService);
        // init rtc
        boolean rtcVideo = createRTCVideo(appId, audioFrameProcessor);

        if (!rtcVideo || mRTCVideo == null) {
            log.error("fail to connect rtc service, roomId {}", task.getRoomId());
            return false;
        }

        // 开始入房
        boolean rtcResult = joinRtcRoom();
        if(!rtcResult) {
            log.error("fail to start call, join room fail, roomId {}", task.getRoomId());
            return false;
        }
        log.info("init rtc service complete, roomId {}, taken {} ms", task.getRoomId(), (System.currentTimeMillis() - l));
        return true;
    }

    @Override
    public void clean() {
        if(!contextService.isRtcChannel(contextService.getTask())) {
            return;
        }
        long l = System.currentTimeMillis();
        try {
            leaveRoom();
            destroyRoom();
            if(mRTCVideo != null) {
                //clean RTCVideo
                RTCVideo.destroyRTCVideo(mRTCVideo);
                this.mRTCVideo = null;
            }
        } catch (Exception e) {
            log.error("leave room exception, roomId {}", roomId, e);
        } finally {
            log.info("clean rtc service complete, roomId {}, taken {} ms", roomId, (System.currentTimeMillis() - l));
        }

        this.appId = null;
        this.appKey = null;
    }

    @Override
    public int order() {
        return 0;
    }

    /**
     * 开始RTC服务，开始计费
     *
     * @return
     */
    public boolean joinRtcRoom() {
        long l = System.currentTimeMillis();
        try {
            // create room
            boolean createRoomResult = createRoom(roomId, new RtcRoomEventHandler(contextService), success -> {
            });

            if (!createRoomResult) {
                return false;
            }

            String botId = "bot_" + targetUserId;
            String token = calcToken(appId, appKey, roomId, botId);
            log.info("bot joinRtcRoom token, roomId {}, appId {}, botId {}, token {}", roomId, appId, botId, token);

            // join room
            boolean joinRoomResult = joinRoom(token, botId, "", true, true, false);
            if (!joinRoomResult) {
                return false;
            }

            // 推流线程启动
            PushAudioStreamTask pushAudio = new PushAudioStreamTask();
            new Thread(pushAudio).start();

            log.info("bot joinRtcRoom complete, roomId {}, targetId {}, taken(ms) {}", roomId, targetUserId, (System.currentTimeMillis() - l));
        } catch (Exception e) {
            log.error("fail to init rtc service, roomId {}, targetId {}", roomId, targetUserId, e);
            return false;
        }
        return true;
    }

    private boolean createRTCVideo(String appId, IAudioFrameProcessor audioFrameProcessor) {
        log.info("create rtc video, appId {}, roomId {}", appId, roomId);
        long l = System.currentTimeMillis();
        mRTCVideo = RTCVideo.createRTCVideo(appId, new LoggerRtcEventHandler(), null);
        log.info("after create rtc video, appId {}, roomId {}, taken {} ms", appId, roomId, (System.currentTimeMillis() - l));

        if (mRTCVideo == null) {
            log.error("fail to init rtc, appId {}, roomId {}", appId, roomId);
            return false;
        }

        // 注册音频处理器，每 10 ms 收到此回调
        mRTCVideo.registerAudioProcessor(audioFrameProcessor);
        // 设置自定义声音采集
        mRTCVideo.setAudioSourceType(AudioSourceType.AUDIO_SOURCE_TYPE_EXTERNAL);

        //设置回调音频格式
        AudioFormat audioFormat = new AudioFormat(AudioSampleRate.AUDIO_SAMPLE_RATE_16000, AudioChannel.AUDIO_CHANNEL_MONO);
        // 开启订阅的远端每个用户混音前的音频数据回调
        mRTCVideo.enableAudioProcessor(AudioFrameProcessorMethod.kAudioFrameProcessorMethodRemoteUser, audioFormat);
        return true;
    }

    private boolean createRoom(String roomId, IRTCRoomEventHandler mRoomEventHandler, Consumer<Boolean> result) {
        log.debug("create rtc room, roomId {}", roomId);

        mRTCRoom = mRTCVideo.createRTCRoom(roomId);
        if (mRTCRoom == null) {
            log.error("fail to create rtc room, roomId {}", roomId);
            return false;
        }

        mRTCRoom.setRTCRoomEventHandler(mRoomEventHandler);
        result.accept(mRTCRoom != null);
        return true;
    }

    private boolean joinRoom(String token, String userId, String extraInfo, boolean isAutoPublish, boolean isAutoSubscribeAudio, boolean isAutoSubscribeVideo) {
        log.debug("bot joinRoom roomId {}, uid {}", roomId, userId);


        RTCRoomConfig config = new RTCRoomConfig(
                ChannelProfile.CHANNEL_PROFILE_COMMUNICATION,
                isAutoPublish,
                isAutoSubscribeAudio,
                isAutoSubscribeVideo);
        int result = mRTCRoom.joinRoom(token, new UserInfo(userId, extraInfo), config);

        if (result == 0) {
            log.info("bot joinRoom success, roomId {}, uid {}", roomId, userId);
            return true;
        } else if (result == -4) {
            log.error("bot joinRoom failed, 用户已在房间内, roomId {}, uid {}", roomId, userId);
            return true;
        } else {
            log.error("bot joinRoom failed, roomId {}, uid {}, result {}", roomId, userId, result);
            return false;
        }
    }

    private void leaveRoom() {
        log.info("leaveRoom begin, roomId {} ", roomId);
        if (mRTCRoom == null) {
            log.error("mRTCRoom is null, skip, roomId {} ", roomId);
            return;
        }
        mRTCRoom.leaveRoom();
        log.debug("leaveRoom end, roomId {}", roomId);
    }

    private void destroyRoom() {
        log.debug("destroyRoom: roomId {} ", roomId);
        if (mRTCRoom == null) {
            log.info("mRTCRoom is null, skip, roomId {} ", roomId);
            return;
        }
        mRTCRoom.destroy();
        mRTCRoom = null;
    }

    private String calcToken(@NonNull String appId, @NonNull String appKey, @NonNull String roomId, @NonNull String userId) {
        return VolcanoUtil.getRtcToken(appId, appKey, roomId, userId);
    }


    /**
     * VAD检测线程
     */
//    class VADDetectTask implements Runnable{
//        @Override
//        public void run() {
//            while (true) {
//                if(!contextService.isWorking()){
//                    log.info("VADDetectTask quit");
//                    break;
//                }
//                // 1024 > 320*4
//                try {
//                    Thread.sleep(30);
//                } catch (InterruptedException e) {
//                    throw new RuntimeException(e);
//                }
//                // 拷贝数据到数组
//                byte[] dequeue = contextService.getRtcToVadBuffer().dequeue(RtcConstants.VAD_BATCH_SAMPLE_COUNT);
//                if(dequeue == null || dequeue.length == 0) {
//                    continue;
//                }
//                // 开始vad 检测
//                long l = System.currentTimeMillis();
//                handleHumanVoice(dequeue);
//                log.debug("vad check cost: {} ms", System.currentTimeMillis() - l);
//            }
//        }
//
//        /**
//         * 人声处理
//         *
//         * @param data 声音数据
//         * @return
//         */
//        private void handleHumanVoice(byte[] data) {
//            contextService.getVadService().apply(data);
//        }
//    }

    /**
     * 推流到RTC线程，10ms打包
     */
    class PushAudioStreamTask implements Runnable {
        int PACKAGE_TIME = 10;
        final AudioSampleRate sampleRate = AudioSampleRate.AUDIO_SAMPLE_RATE_16000;
        // 10毫秒的采样数据长度
        final int frameLength = sampleRate.value * PACKAGE_TIME / 1000;
        final int channels = RtcConstants.RTC_AUDIO_CHANNELS;
        private boolean speakFlag = false;


        @Override
        public void run() {
            int batch_size = frameLength * channels * 2;

            while (true) {
                if(!contextService.isWorking()){
                    log.info("rtc PushAudioStreamTask quit, roomId {}", roomId);
                    break;
                }

                if(!contextService.getHasRtcDataToPush().get()) {
                    Utils.sleep(100);
                    continue;
                }

                // 优先处理欢迎语数据
                byte[] dequeue = contextService.getWelcomeBuffer().dequeue(batch_size);
                if (dequeue == null || dequeue.length == 0) {
                    // 判定欢迎语发送完毕
                    if (contextService.isWelcomeSent() && !contextService.isWelcomeComplete()) {
                        contextService.setWelcomeComplete(true);
                    }
                    // 从正常buffer获取数据
                    dequeue = contextService.getTtsToRtcBuffer().dequeue(batch_size);
                }

                // 没有待发送的数据
                if (dequeue == null || dequeue.length == 0) {
                    // 标识推送完成
                    contextService.getHasRtcDataToPush().set(false);

                    // 标识AI说话完成
                    if(this.speakFlag) {
                        if(contextService.isHookStatus()) {
                            log.info("audio sent complete in hook status, call tel hangup, roomId {}", contextService.getTask().getRoomId());
                            Utils.sleep(100);
                            // 挂机，清理
                            contextService.hangupManual(roomId, "rtc", System.currentTimeMillis());
                            break;
                        }else {
                            // 追问，如果AI内容发送完毕, 开始等待访客说话
                            log.info("ai speech complete, start waiting user speak, roomId {}", roomId);
                            contextService.setAiSpeakEndTime(System.currentTimeMillis());
                        }
                    }

                    this.speakFlag = false;
                    Utils.sleep(PACKAGE_TIME);
                    continue;
                }

                // 有待发送数据
                log.trace("push rtc, dequeue, roomId {}, length {}", roomId, dequeue.length);
                this.speakFlag = true;
                AudioFrame frame = new AudioFrame(dequeue, frameLength, sampleRate, AudioChannel.fromId(RtcConstants.RTC_AUDIO_CHANNELS));
                try {
                    int i = mRTCVideo.pushExternalAudioFrame(frame);
                    // 录制方便排查问题
                    contextService.getRecordService().recordAI(dequeue);

                    if (i != 0 && i != -2) {
                        log.error("push audio frame fail, code {}, dequeue size {}, frameLength {}, sample rate {}, channel {} ", i, dequeue.length, frameLength, sampleRate.value, AudioChannel.fromId(RtcConstants.RTC_AUDIO_CHANNELS));
                    }
                } catch (IllegalStateException e) {
                    log.error("pushExternalAudioFrame failed, roomId {}", contextService.getTask().getRoomId(), e);
                }
                Utils.sleep(PACKAGE_TIME);
            }
        }
    }
}
