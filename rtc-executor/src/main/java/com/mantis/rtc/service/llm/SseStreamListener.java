package com.mantis.rtc.service.llm;


import com.mantis.rtc.service.CallContextService;
import lombok.extern.slf4j.Slf4j;

/**
 * sse listener
 * <AUTHOR>
 */
@Slf4j
public class SseStreamListener extends AbstractStreamListener {
    public SseStreamListener(ChatContextServiceMemoryImpl chatContextService, CallContextService contextService, String roomId, String msgId, String userMsg) {
        super(chatContextService, contextService,roomId, msgId, userMsg);
    }

    /**
     * AI的流式回复
     * @param message the new message
     */
    @Override
    public void onMsg(String message) {
        try{
            contextService.getTtsService().streamingCall(message, msgId);
        }catch (Exception e) {
            log.error("fail to call streamingCall of tts", e);
        }
    }

    @Override
    public void onError(Throwable throwable, String response) {
        log.error("SseStreamListener onError, roomId {}, msgId {}, response {}", roomId, msgId, response, throwable);
        contextService.setSilence(false, "call llm onError is too short", msgId);
        // 标识AI说话完毕，启动追问
        contextService.setAiSpeakEndTime(System.currentTimeMillis());
    }

}
