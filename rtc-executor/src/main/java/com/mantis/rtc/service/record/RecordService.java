package com.mantis.rtc.service.record;


import com.mantis.micor.pojo.CallTask;
import com.mantis.rtc.config.VadConfig;
import com.mantis.rtc.service.CallContextService;
import com.mantis.rtc.service.TaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;

@Service
@Slf4j
public class RecordService implements TaskService {
    @Autowired
    private VadConfig config;

    @Autowired
    private CallContextService contextService;

    /**
     * user录制文件名
     */
    private FileOutputStream userOutputStream = null;
    /**
     * AI的略知
     */
    private FileOutputStream aiOutputStream = null;


    public void recordAI(byte[] data) {
        try{
            if(!config.getRecord() && !contextService.isAgoraRtcChannel(contextService.getTask())) {
                return;
            }

            if(aiOutputStream == null) {
                return;
            }

            try {
                aiOutputStream.write(data);
            } catch (IOException e) {
                log.error("fail to write data into record file", e);
            }
        }catch (Exception e) {
            log.error("fail to record" ,e);
        }
    }

    public void recordUser(byte[] data) {
        try{
            // 不录制
            if(!config.getRecord() && !contextService.isAgoraRtcChannel(contextService.getTask())) {
                return;
            }

            if(userOutputStream == null) {
                return;
            }

            try {
                userOutputStream.write(data);
            } catch (IOException e) {
                log.error("fail to write data into record file, user", e);
            }
        }catch (Exception e) {
            log.error("fail to record, user" ,e);
        }
    }

    @Override
    public boolean init(CallTask task) {
        // 不录制
        if(!config.getRecord() && !contextService.isAgoraRtcChannel(contextService.getTask())) {
            return true;
        }

        String roomId = task.getRoomId();
        String folder = String.format("/mantis/records/%s", roomId);
        // 检查目录是否存在
        if(!new File(folder).exists()) {
            boolean mkdirs = new File(folder).mkdirs();
            if(!mkdirs) {
                log.error("fail to create record folder, {}", folder);
            }
        }
        // AI 的录制
        String playback_file_name = folder + "/ai.pcm";
        try {
            aiOutputStream = new FileOutputStream(playback_file_name);
        } catch (FileNotFoundException e) {
            log.error("fail to init record service, ai", e);
        }

        // 学员的录制
        String playback_file_name_user = folder + "/user.pcm";
        try {
            userOutputStream = new FileOutputStream(playback_file_name_user);
        } catch (FileNotFoundException e) {
            log.error("fail to init record service, user", e);
        }

        return true;
    }

    @Override
    public void clean() {
        if(aiOutputStream == null) {
            return;
        }

        try {
            aiOutputStream.close();
        } catch (IOException e) {
            log.error("fail to close record file, ai", e);
        } finally {
            aiOutputStream = null;
        }


        if(userOutputStream == null) {
            return;
        }
        try {
            userOutputStream.close();
        } catch (IOException e) {
            log.error("fail to close record file, user", e);
        } finally {
            userOutputStream = null;
        }

    }

    @Override
    public int order() {
        return 3;
    }

}
