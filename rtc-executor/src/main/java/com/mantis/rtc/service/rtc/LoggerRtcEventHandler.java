package com.mantis.rtc.service.rtc;

import com.ss.bytertc.engine.RTCVideo;
import com.ss.bytertc.engine.SysStats;
import com.ss.bytertc.engine.handler.IRTCVideoEventHandler;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class LoggerRtcEventHandler implements IRTCVideoEventHandler {
    void info(String message) {
        log.info("[RTCVideo #" + "][Video]" + message);
    }
    /**
     * 控制是否跳过频繁打印的日志
     */
    static final boolean SKIP_LOG = true;

    public LoggerRtcEventHandler() {
    }

    @Override
    public void onError(int err) {
        log.error("rtc onError, code: {}", err);
    }

    @Override
    public void onWarning(int warn) {
        info("onWarning: " + warn + "; desc=" + RTCVideo.getErrorDescription(warn));
    }

    @Override
    public void onConnectionStateChanged(int state, int reason) {
//        info("onConnectionStateChanged: state=" + state + "; reason=" + reason);
    }

    @Override
    public void onNetworkTypeChanged(int type) {
//        info("onNetworkTypeChanged: state=" + type);
    }

    @Override
    public void onSysStats(SysStats stats) {
        if (SKIP_LOG) return;
//        info("onSysStats: state=" + stats);
    }

    @Override
    public void onCloudProxyConnected(int interval) {
//        info("onCloudProxyConnected: interval=" + interval);
    }


}
