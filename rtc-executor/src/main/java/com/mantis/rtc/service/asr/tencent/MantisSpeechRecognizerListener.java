package com.mantis.rtc.service.asr.tencent;

import com.google.gson.Gson;
import com.mantis.rtc.pojo.Constants;
import com.mantis.rtc.service.CallContextService;
import com.tencent.asrv2.SpeechRecognizerListener;
import com.tencent.asrv2.SpeechRecognizerResponse;
import com.tencent.asrv2.SpeechRecognizerResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

//tips：回调方法中应该避免进行耗时操作，如果有耗时操作建议进行异步处理否则会影响websocket请求处理

@Slf4j
public class MantisSpeechRecognizerListener extends SpeechRecognizerListener {
    private final TencentAsrService tencentAsrService;
    private final CallContextService contextService;
    private final String roomId;
    private String msgId;

    /**
     * 是否首token
     */
    private boolean isFirst = false;

    public MantisSpeechRecognizerListener(CallContextService contextService, TencentAsrService tencentAsrService, String roomId) {
        this.tencentAsrService = tencentAsrService;
        this.contextService = contextService;
        this.roomId = roomId;
    }

    @Override
    public void onRecognitionStart(SpeechRecognizerResponse response) {//首包回调
        log.debug("{} voice_id:{},{}", "onRecognitionStart", response.getVoiceId(), new Gson().toJson(response));
        this.msgId = response.getVoiceId();
    }

    @Override
    public void onSentenceBegin(SpeechRecognizerResponse response) {//一段话开始识别 slice_type=0
        log.debug("{} voice_id:{},{}", "onSentenceBegin", response.getVoiceId(), new Gson().toJson(response));
    }

    /**
     * {
     * "code": 0,
     * "message": "success",
     * "voice_id": "RnKu9FODFHK5FPpsrN",
     * "message_id": "RnKu9FODFHK5FPpsrN_11_0",
     * "result": {
     * "slice_type": 0,
     * "index": 0,
     * "start_time": 0,
     * "end_time": 1240,
     * "voice_text_str": "实时",
     * "word_size": 0,
     * "word_list": []
     * }
     * }
     *
     * @param response
     */

    @Override
    public void onRecognitionResultChange(SpeechRecognizerResponse response) {//一段话识别中，slice_type=1,voice_text_str 为非稳态结果(该段识别结果还可能变化)
        log.debug(" {} voice_id:{},{}", "onRecognitionResultChange", response.getVoiceId(), new Gson().toJson(response));
        SpeechRecognizerResult result = response.getResult();
        String voiceId = response.getVoiceId();
        if (result == null) {
            return;
        }

        String voiceTextStr = result.getVoiceTextStr();
        if (StringUtils.isBlank(voiceTextStr)) {
            return;
        }

        if (!isFirst) {
            isFirst = true;
            contextService.setMsgInfoTrack(voiceId, "asr_first_token", System.currentTimeMillis());
        }

        log.debug("tencent_asr changed: {}, roomId {}, msgId {}", voiceTextStr, roomId, voiceId);
        tencentAsrService.setResult(voiceId, voiceTextStr);
    }

    @Override
    public void onSentenceEnd(SpeechRecognizerResponse response) {//一段话识别结束，slice_type=2,voice_text_str 为稳态结果(该段识别结果不再变化)
        SpeechRecognizerResult result = response.getResult();
        String voiceId = response.getVoiceId();
        if (result == null) {
            return;
        }

        String voiceTextStr = result.getVoiceTextStr();

        if (!contextService.getMsgInfoTrack(voiceId).isValidMsg()) {
            log.info("tencent asr final result, but invalid msg, skip, roomId {}, msgId {}, content {}", roomId, voiceId, voiceTextStr);
            return;
        }

        contextService.setMsgInfoTrack(voiceId, "asr_complete", System.currentTimeMillis());

        if (StringUtils.isNotBlank(voiceTextStr)) {
            log.info("asr_result, tencent result: {}, roomId {}, msgId {}", voiceTextStr, roomId, voiceId);
            contextService.setMsgInfoTrack(voiceId, "asrFinalMsg", voiceTextStr);
        } else {
            log.info("asr_result, tencent result is empty, roomId {}, msgId {}", roomId, voiceId);
            contextService.setMsgInfoTrack(msgId, "asrFinalMsg", "");
        }

        // 如果腾讯识别到结果，直接返回，否则等待火山的结果
        if (StringUtils.isNotBlank(voiceTextStr)) {
            Constants.executor.submit(new Runnable() {
                public void run() {
                    contextService.getLlmService().doLLm(msgId, voiceTextStr);
                }
            });
        } else {
            log.info("tencent asr result is empty, wait for volcan asr result, roomId {}, msgId {}", roomId, msgId);
            boolean flag = false;
            // 最多等1秒，100ms检查一次
            for (int i = 0; i < 10; i++) {
                String volcanAsrResult = contextService.getVolcanAsrService().getResult(msgId);
                if (StringUtils.isBlank(volcanAsrResult)) {
                    try {
                        Thread.sleep(100);
                    } catch (InterruptedException e) {
                        log.error("fail to sleep", e);
                    }
                } else {
                    flag = true;
                    Constants.executor.submit(new Runnable() {
                        public void run() {
                            contextService.getLlmService().doLLm(msgId, volcanAsrResult);
                        }
                    });
                    break;
                }
            }
            // 如果火山1秒内没有结果，则直接调用llm
            if(!flag){
                contextService.getLlmService().doLLm(msgId, null);
            }
        }

    }

    @Override
    public void onRecognitionComplete(SpeechRecognizerResponse response) {//识别完成回调 即final=1, response没有识别文本
        log.info("onRecognitionComplete msgId:{}, roomId: {}, close recognizer", response.getVoiceId(), roomId);
        recognizer.close();
        // 移除
        tencentAsrService.removeSpeechRecognizer(response.getVoiceId());
    }

    @Override
    public void onFail(SpeechRecognizerResponse response) {//失败回调
        log.error("{} voice_id:{},{}，roomId {}, msgId {}", "onFail", response.getVoiceId(), new Gson().toJson(response), roomId, msgId);
    }

    @Override
    public void onMessage(SpeechRecognizerResponse response) {//所有消息都会回调该方法
    }
}
