package com.mantis.rtc.service.tts;

public interface TTSStreamService {
    /**
     * 同步语音合成，支持cache
     * @param sentence
     * @param roomId
     * @param msgId
     */
    void call(String sentence, String roomId, String msgId, boolean complete);

    void streamingCall(String sentence, String msgId);

    /**
     * 开始语音合成
     * @param roomId
     * @param msgId
     */
    void onChatStreamStart(String roomId, String msgId);

    /**
     * 结束语音合成
     * @param msgId
     */
    void completeSession(String msgId);

}
