package com.mantis.rtc.service.asr.volcan;

import com.google.gson.Gson;
import okhttp3.WebSocket;
import okio.ByteString;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

public class VolcanAsrBase {
    static final byte PROTOCOL_VERSION = 0b0001;
    static final byte DEFAULT_HEADER_SIZE = 0b0001;
    static final byte FULL_CLIENT_REQUEST = 0b0001;
    static final byte AUDIO_ONLY_REQUEST = 0b0010;
    static final byte FULL_SERVER_RESPONSE = 0b1001;
    static final byte SERVER_ACK = 0b1011;
    static final byte SERVER_ERROR_RESPONSE = 0b1111;
    static final byte POS_SEQUENCE = 0b0001;
    static final byte NEG_WITH_SEQUENCE = 0b0011;
    static final byte JSON = 0b0001;
    static final byte GZIP = 0b0001;


    static void sendAudioSegment(WebSocket webSocket, byte[] buffer, int len, boolean isLast, int seq) {
        byte messageTypeSpecificFlags = isLast ? NEG_WITH_SEQUENCE : POS_SEQUENCE;
        byte[] header = getHeader(AUDIO_ONLY_REQUEST, messageTypeSpecificFlags, JSON, GZIP, (byte) 0);
        byte[] sequenceBytes = intToBytes(seq);
        byte[] payloadBytes = gzipCompress(buffer, len);
        byte[] payloadSize = intToBytes(payloadBytes.length);

        byte[] audioRequest = new byte[header.length + sequenceBytes.length + payloadSize.length + payloadBytes.length];
        System.arraycopy(header, 0, audioRequest, 0, header.length);
        System.arraycopy(sequenceBytes, 0, audioRequest, header.length, sequenceBytes.length);
        System.arraycopy(payloadSize, 0, audioRequest, header.length + sequenceBytes.length, payloadSize.length);
        System.arraycopy(payloadBytes, 0, audioRequest, header.length + sequenceBytes.length + payloadSize.length,
                payloadBytes.length);

        webSocket.send(ByteString.of(audioRequest));
    }

    // 辅助方法保持不变
    static byte[] getHeader(byte messageType, byte messageTypeSpecificFlags,
                                    byte serialMethod, byte compressionType, byte reservedData) {
        final byte[] header = new byte[4];
        header[0] = (byte) ((PROTOCOL_VERSION << 4) | DEFAULT_HEADER_SIZE);
        header[1] = (byte) ((messageType << 4) | messageTypeSpecificFlags);
        header[2] = (byte) ((serialMethod << 4) | compressionType);
        header[3] = reservedData;
        return header;
    }

    static byte[] intToBytes(int a) {
        return new byte[] {
                (byte) ((a >> 24) & 0xFF),
                (byte) ((a >> 16) & 0xFF),
                (byte) ((a >> 8) & 0xFF),
                (byte) (a & 0xFF)
        };
    }

    static int bytesToInt(byte[] src) {
        if (src == null || (src.length != 4)) {
            throw new IllegalArgumentException("Invalid byte array for int conversion");
        }
        return ((src[0] & 0xFF) << 24)
               | ((src[1] & 0xff) << 16)
               | ((src[2] & 0xff) << 8)
               | ((src[3] & 0xff));
    }

    static byte[] gzipCompress(byte[] src) {
        return gzipCompress(src, src.length);
    }

    static byte[] gzipCompress(byte[] src, int len) {
        if (src == null || len == 0) {
            return new byte[0];
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        try (GZIPOutputStream gzip = new GZIPOutputStream(out)) {
            gzip.write(src, 0, len);
        } catch (IOException e) {
            e.printStackTrace();
            return new byte[0];
        }
        return out.toByteArray();
    }

    static byte[] gzipDecompress(byte[] src) {
        if (src == null || src.length == 0) {
            return null;
        }
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        ByteArrayInputStream ins = new ByteArrayInputStream(src);
        try (GZIPInputStream gzip = new GZIPInputStream(ins)) {
            byte[] buffer = new byte[256];
            int len;
            while ((len = gzip.read(buffer)) > 0) {
                out.write(buffer, 0, len);
            }
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
        return out.toByteArray();
    }
}
