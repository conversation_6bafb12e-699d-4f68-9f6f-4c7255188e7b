package com.mantis.rtc.service.asr.volcan;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.Gson;
import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;
import com.mantis.micor.pojo.CallConfig;
import com.mantis.micor.pojo.CallTask;
import com.mantis.rtc.buffer.VadToAsrBuffer;
import com.mantis.rtc.pojo.Constants;
import com.mantis.rtc.service.CallContextService;
import com.mantis.rtc.service.TaskService;
import com.mantis.rtc.service.asr.AsrService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okio.ByteString;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import java.nio.ByteBuffer;
import java.util.*;
import java.util.concurrent.*;

@Slf4j
@Service
public class VolcanAsrService extends VolcanAsrBase  implements TaskService, AsrService{
    @Autowired
    private CallContextService contextService;

    @Autowired
    @Qualifier("volcanAsrWebsocketClient")
    private OkHttpClient okHttpClient;

    private String roomId = "";

    private CallTask task;

    private CallConfig.VolcanAsr volcanAsr;


    /**
     * websocket 连接池，用于缓存连接
     */
    private final Queue<WebSocket> queue = new ArrayBlockingQueue<>(10);

    /**
     * msgId与对应使用的websocket映射
     */
    private final Map<String, WebSocket> msgIdWebsocketMap = new ConcurrentHashMap<>();
    /**
     * websocket 对应的上下文： seq， msgId, init 状态
     */
    private final Map<WebSocket, WebsocketInfo> websocketInfoMap = new ConcurrentHashMap<>();


    /**
     * asr结果， msgId, Asr文本
     */
    private final Map<String, String> msgIdAsrResult = new ConcurrentHashMap<>();

    private final Map<String, VadToAsrBuffer> buffers = new HashMap<>();

    private static final int MAX_POOL_SIZE = 3;

    public final static ThreadPoolExecutor responseQueue = new ThreadPoolExecutor(1, 1,
            60L, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(50),
            new ThreadPoolExecutor.CallerRunsPolicy());


    @Override
    public boolean init(CallTask task) {
        if (contextService.isTelChanel(task)) {
            // 采用率不支持，直接跳过初始化
            return true;
        }

        if(!contextService.isCheckGender())  {
            // 如果不检测性别，直接跳过
            return true;
        }

        CallConfig.ASR asr = task.getCallConfig().getAsr();
        volcanAsr = asr.getVolcan();
        if (volcanAsr == null || StringUtils.isBlank(volcanAsr.getAppId()) || StringUtils.isBlank(volcanAsr.getAppKey())) {
            log.error("invalid volcan asr config, conf {}", asr);
            return true;
        }

        this.task = task;
        this.roomId = task.getRoomId();
        this.websocketInfoMap.clear();
        this.msgIdWebsocketMap.clear();
        this.msgIdAsrResult.clear();
        this.buffers.clear();


        int sampleRate = task.getSampleRate();
        if (sampleRate != 16000) {
            log.error("invalid sample rate, only 16000 support, skip init");
            return false;
        }

        // 提前初始化，放入连接池
        initWebsocketPool(task);

        log.info("volcan asr init complete, roomId {}", task.getRoomId());
        return true;
    }

    @Override
    public void clean() {
        while (true) {
            WebSocket client = queue.poll();
            if (client == null) {
                break;
            }
            try {
                client.cancel();
            } catch (Exception e) {
                log.error("fail to clean asr websocket", e);
            }
        }

        this.msgIdWebsocketMap.clear();
        this.websocketInfoMap.clear();
        this.msgIdAsrResult.clear();
        this.buffers.clear();
        log.info("volcan asr clean complete, roomId {}", roomId);
    }

    @Override
    public int order() {
        return 3;
    }

    /**
     * 将声音数据推送给ASR
     *
     * @param msgId
     * @param data
     */
    @Override
    public void push(String msgId, byte[] data) {
        if (buffers.get(msgId) == null) {
            buffers.put(msgId, new VadToAsrBuffer());
        }
        buffers.get(msgId).enqueue(data);
    }

    @Override
    public boolean startSession(CallTask task, String msgId) {
        WebSocket webSocket = fetchWebsocketFromQueue(task, msgId);
        if (webSocket == null) {
            log.error("volcan asr startSession, asr websocket is null, roomId {}, skip", roomId);
            return false;
        }
        // seq 从1开始
        int seq = 1;

        // send full client request
        // step 1: append payload json string
        JsonObject user = new JsonObject();
        user.addProperty("uid", msgId);

        JsonObject audio = new JsonObject();
        audio.addProperty("format", "pcm"); //
        audio.addProperty("sample_rate", task.getSampleRate());
        audio.addProperty("bits", 16);
        audio.addProperty("channel", 1);
        audio.addProperty("codec", "raw");

        JsonObject request = new JsonObject();
        request.addProperty("model_name", "bigmodel");
        request.addProperty("model_version", "400");

        // 默认为false， 启用标点
        request.addProperty("enable_punc", true);
        request.addProperty("enable_emotion_detection", true);
        request.addProperty("enable_gender_detection", true);


        // 构建context
        JsonArray context_datas = new JsonArray();
        JsonObject context_data = new JsonObject();
        context_data.addProperty("text", "这是一个实时语音对话，只会出现中文，说话人为老年人，说话可能会不清晰，可能会是方言");
        context_datas.add(context_data);

        JsonObject context = new JsonObject();
        context.addProperty("context_type", "dialog_ctx");
        context.add("context_data", context_datas);

        JsonObject corpus = new JsonObject();
        corpus.addProperty("context", context.toString());
        request.add("corpus", corpus);

        JsonObject payload = new JsonObject();
        payload.add("user", user);
        payload.add("audio", audio);
        payload.add("request", request);

        String payloadStr = payload.toString();
        log.info("volcan asr start session request {}, roomId {}, msgId {}", payloadStr, roomId, msgId);
        // step2: 压缩 payload 字段。
        final byte[] payloadBytes = gzipCompress(payloadStr.getBytes());
        // step3:组装 fullClientRequest；fullClientRequest= header+ sequence + payload
        byte[] header = getHeader(FULL_CLIENT_REQUEST, POS_SEQUENCE, JSON, GZIP, (byte) 0);
        final byte[] payloadSize = intToBytes(payloadBytes.length);

        byte[] seqBytes = intToBytes(seq); // 使用传入的序列号
        final byte[] fullClientRequest = new byte[header.length + seqBytes.length + payloadSize.length + payloadBytes.length];
        System.arraycopy(header, 0, fullClientRequest, 0, header.length);
        System.arraycopy(seqBytes, 0, fullClientRequest, header.length, seqBytes.length);
        System.arraycopy(payloadSize, 0, fullClientRequest, header.length + seqBytes.length, payloadSize.length);
        System.arraycopy(payloadBytes, 0, fullClientRequest, header.length + seqBytes.length + payloadSize.length,
                payloadBytes.length);
        boolean send = webSocket.send(ByteString.of(fullClientRequest));

        if (send) {
            log.info("volcan asr start session success, start push thread, roomId {}, msgId {}", roomId, msgId);
            Thread pushThread = new Thread(new PushToAsrTask(msgId));
            pushThread.start();
        }
        return send;
    }

    @Override
    public String getResult(String msgId) {
        return msgIdAsrResult.get(msgId);
    }


    private void onAsrDataSentComplete(String msgId) {
        log.info("volcan asr, onAsrDataSentComplete, roomId {}, msgId {}", task.getRoomId(), msgId);
        contextService.setMsgInfoTrack(msgId, "send_volcan_asr_data_complete", System.currentTimeMillis());
    }

    /**
     * 发送语音数据到asr
     * @param task
     * @param msgId
     * @param buffer
     * @param isLast
     */
    private void doAsr(CallTask task, String msgId, ByteBuffer buffer, boolean isLast) {
        WebSocket webSocket = fetchWebsocketFromQueue(task, msgId);
        if (webSocket == null) {
            log.error("doAsr, asr websocket is null, roomId {}, msgId {}, skip", roomId, msgId);
            return;
        }
        // 标识asr 完成
        if (isLast) {
            onAsrDataSentComplete(msgId);
        }

        byte[] data = buffer.array();
        int seq = getAndIncrease(webSocket);
        log.debug("send volcan asr data, msgId {}, isLast {}, seq: {}", msgId, isLast, isLast ? -seq : seq);
        sendAudioSegment(webSocket, data, data.length, isLast, isLast ? -seq : seq);
    }


    private void initWebsocketPool(CallTask task) {
        for (int i = 0; i < MAX_POOL_SIZE - queue.size(); i++) {
            Constants.ASR_TTS_EXECUTOR.submit(new Runnable() {
                @Override
                public void run() {
                    connectAsr(task);
                }
            });
        }
    }

    private WebSocket fetchWebsocketFromQueue(CallTask task, String msgId) {
        if (msgIdWebsocketMap.containsKey(msgId)) {
            return msgIdWebsocketMap.get(msgId);
        }

        // 分配
        for (int i = 0; i < 4; i++) {
            WebSocket webSocket = queue.poll();
            if (webSocket != null) {
                String logId = onWebsocketAlloc(webSocket, msgId);
                log.info("fetch asr websocket success, left {}, msgId {},  roomId {}, logId {}", queue.size(), msgId, task.getRoomId(), logId);
                // 每次调用后，都尝试维持连接池的数量
                initWebsocketPool(task);
                return webSocket;
            }

            log.info("fail to get asr websocket, will retry {}, roomId {}", i + 1, task.getRoomId());
            try {
                Thread.sleep(200);
            } catch (InterruptedException e) {
                log.error("fail to sleep", e);
            }
        }


        log.error("fail to get asr websocket, msgId {}, roomId {}", msgId, task.getRoomId());
        return null;
    }

    /**
     * 连接完成的处理
     *
     * @param webSocket
     */
    private void onWebsocketConnected(WebSocket webSocket, String logId) {
        // 加入队列
        queue.add(webSocket);
        websocketInfoMap.put(webSocket, WebsocketInfo.builder().seq(1).logId(logId).isReady(false).build());
        log.info("volcan asr onOpen, roomId {}, logId {}, pool size now: {}", roomId, logId, queue.size());
    }

    /**
     * 标识是否初始化完成
     *
     * @param webSocket
     */
    private void onWebsocketReady(WebSocket webSocket) {
        WebsocketInfo websocketInfo = websocketInfoMap.get(webSocket);
        websocketInfo.setReady(true);
    }

    /**
     * 关联msgId
     *
     * @param webSocket
     * @param msgId
     */
    private String onWebsocketAlloc(WebSocket webSocket, String msgId) {
        msgIdWebsocketMap.put(msgId, webSocket);
        WebsocketInfo websocketInfo = websocketInfoMap.get(webSocket);
        websocketInfo.setMsgId(msgId);
        return websocketInfo.getLogId();
    }

    private boolean isMsgAsrReady(String msgId) {
        WebSocket webSocket = msgIdWebsocketMap.get(msgId);
        if (webSocket == null) {
            return false;
        }
        WebsocketInfo websocketInfo = websocketInfoMap.get(webSocket);
        return websocketInfo.isReady();
    }

    /**
     * 返回当前值，并自增
     *
     * @param webSocket
     * @return
     */
    private Integer getAndIncrease(WebSocket webSocket) {
        WebsocketInfo websocketInfo = websocketInfoMap.get(webSocket);
        int currentSeq = websocketInfo.getSeq();
        websocketInfo.setSeq(currentSeq + 1);
        return websocketInfo.getSeq();
    }

    private void setResult(String msgId, String result) {
        msgIdAsrResult.put(msgId, result);
        contextService.setMsgInfoTrack(msgId,"volcan_asr_complete", System.currentTimeMillis());
        contextService.setMsgInfoTrack(msgId, "volcanAsrFinalMsg", result);
//        Constants.executor.submit(new Runnable() {
//            public void run() {
//                contextService.getLlmService().doLLm(msgId, result);
//            }
//        });
    }

    private void connectAsr(CallTask task) {
        log.debug("begin connect volcan asr, roomId {}", task.getRoomId());
        final Request request = new Request.Builder()
                .url("wss://openspeech.bytedance.com/api/v3/sauc/bigmodel_nostream")
                .header("X-Api-App-Key", volcanAsr.getAppId())
                .header("X-Api-Access-Key", volcanAsr.getAppKey())
                .header("X-Api-Resource-Id", "volc.bigasr.sauc.duration")
                .header("X-Api-Connect-Id", UUID.randomUUID().toString())
                .build();

        okHttpClient.newWebSocket(request, new WebSocketListener() {
            @Override
            public void onOpen(WebSocket webSocket, Response response) {
                String logId = response.header("X-Tt-Logid");
                onWebsocketConnected(webSocket, logId);
            }

            @Override
            public void onMessage(WebSocket webSocket, String text) {
                super.onMessage(webSocket, text);
            }

            @Override
            public void onMessage(WebSocket webSocket, ByteString bytes) {
                byte[] res = bytes.toByteArray();
                responseQueue.submit(new Runnable() {
                    @Override
                    public void run() {
                        Map<String, Object> result = parserResponse(webSocket, res);
                        if(null == result) {
                            return;
                        }

                        int seq = (int) result.get("sequence");
                        Boolean asrComplete = (Boolean) result.get("asr_complete");

                        // 如果是最后一个包，则可以关闭连接
                        if (seq < 0 || (null!= asrComplete && asrComplete)) {
                            // 如果收到最后一包的回复，则关闭websocket，清理缓存
                            WebsocketInfo websocketInfo = websocketInfoMap.get(webSocket);
                            if (websocketInfo != null) {
                                String msgId = websocketInfo.getMsgId();
                                log.info("volcan asr complete，关闭websocket roomId {}, msgId:{}", roomId, msgId);
                                msgIdWebsocketMap.remove(msgId);
                            }
                            queue.remove(webSocket); // 显式移除失效连接
                            websocketInfoMap.remove(webSocket);
                            webSocket.close(1000, "endSession");
                        }
                    }
                });
            }

            @Override
            public void onClosing(WebSocket webSocket, int code, String reason) {
                super.onClosing(webSocket, code, reason);
            }

            @Override
            public void onClosed(WebSocket webSocket, int code, String reason) {
                super.onClosed(webSocket, code, reason);
                log.info("volcan asr onClosed： code:" + code + " reason:" + reason);
                WebsocketInfo websocketInfo = websocketInfoMap.get(webSocket);
                if (websocketInfo != null) {
                    String msgId = websocketInfo.getMsgId();
                    msgIdWebsocketMap.remove(msgId);
                }
                websocketInfoMap.remove(webSocket);
                queue.remove(webSocket); // 显式移除失效连接
            }

            @Override
            public void onFailure(WebSocket webSocket, Throwable t, Response response) {
                super.onFailure(webSocket, t, response);

                WebsocketInfo websocketInfo = websocketInfoMap.get(webSocket);
                String logId = "";
                String msgId = "";
                if (websocketInfo != null) {
                    logId = websocketInfo.getLogId();
                    msgId = websocketInfo.getMsgId();
                }

                // 如果出现这个日志，说明之前asr没有返回识别结果，需要将静默清除掉
//                contextService.setSilence(false, "asr websocket failed", msgId);

                // 打印日志
                String error = response == null ? "null" : response.toString();
                log.error("volcan asr onFailure,  Throwable: {}, Response: {}, roomId {}, msgId {}, logId {}", t.getMessage(), error, roomId, msgId, logId);

                // 关闭连接，清理资源
                if (StringUtils.isNotBlank(msgId)) {
                    msgIdWebsocketMap.remove(msgId);
                }
                queue.remove(webSocket); // 显式移除失效连接
                websocketInfoMap.remove(webSocket);
                webSocket.close(1000, "endSession");
            }
        });
    }


    Map<String, Object> parserResponse(WebSocket webSocket, byte[] res) {
        if (res == null || res.length == 0) {
            return null;
        }
        // 当符号位为1时进行 >> 运算后高位补1（预期是补0），导致结果错误，所以增加个数再与其& 运算，目的是确保高位是补0.
        final byte num = 0b00001111;
        Map<String, Object> result = new HashMap<>();

        // 解析头部
        int protocol_version = (res[0] >> 4) & num;
        result.put("protocol_version", protocol_version);
        int header_size = res[0] & 0x0f;
        result.put("header_size", header_size);

        int message_type = (res[1] >> 4) & num;
        result.put("message_type", message_type);
        int message_type_specific_flags = res[1] & 0x0f;
        result.put("message_type_specific_flags", message_type_specific_flags);
        int serialization_method = (res[2] >> 4) & num;
        result.put("serialization_method", serialization_method);
        int message_compression = res[2] & 0x0f;
        result.put("message_compression", message_compression);
        int reserved = res[3];
        result.put("reserved", reserved);

        // 解析序列号
        byte[] temp = new byte[4];
        System.arraycopy(res, 4, temp, 0, temp.length);
        int sequence = bytesToInt(temp);
        // 如果 0b0011
        if(message_type_specific_flags == 3 || message_type_specific_flags == 2) {
            sequence = - sequence;
        }
        result.put("sequence", sequence);

        // 解析payload
        String payloadStr = null;
        System.arraycopy(res, 8, temp, 0, temp.length);
        int payloadSize = bytesToInt(temp);
        byte[] payload = new byte[res.length - 12];
        System.arraycopy(res, 12, payload, 0, payload.length);

        // Client 发送的 full client request 和 audio only request，服务端都会返回 full server response。
        if (message_type == FULL_SERVER_RESPONSE) {
            if (message_compression == GZIP) {
                payloadStr = new String(gzipDecompress(payload));
            } else {
                payloadStr = new String(payload);
            }

            log.debug("===asr, seq {}, payload {}", sequence, payloadStr);

            // 收到 FULL_SERVER_RESPONSE 说明 Full_REQUEST 调用成功
            onWebsocketReady(webSocket);

            // 找到对应的msgId
            String msgId = websocketInfoMap.get(webSocket).getMsgId();
            if (StringUtils.isBlank(msgId)) {
                log.error("FULL_SERVER_RESPONSE, fail to get msgId by websocket object, roomId {}", task.getRoomId());
                return result;
            }

            JSONObject payloadObj = JSONObject.parseObject(payloadStr);
            if (payloadObj == null) {
                log.error("volcan_asr, invalid payload {}, roomId {}, msgId {}", payloadStr, roomId, msgId);
                return result;
            }
            JSONObject resultObj = payloadObj.getJSONObject("result");
            if (resultObj == null) {
                log.error("volcan_asr, invalid resultObj {}, roomId {}, msgId {}", payloadStr, roomId, msgId);
                return result;
            }

            String text = resultObj.getString("text");
            // 如果是负包，意味着最终结果, 虽然可能为空
            if(sequence < 0) {
                contextService.setMsgInfoTrack(msgId, "volcan_asr_first_token", System.currentTimeMillis());
                log.info("asr_result, volcan result: {}, roomId {}, msgId {}", text, roomId, msgId);
                setResult(msgId, text);
                result.put("asr_complete", Boolean.TRUE);
            }

            // 分句信息
            JSONArray utterances = resultObj.getJSONArray("utterances");
            if (utterances != null && !utterances.isEmpty()) {
                try {
                    parseUtterances(utterances, msgId);
                }catch (Exception e) {
                    log.error("fail to parse utterances {}, roomId {}, msgId {}", utterances, roomId, msgId, e);
                }
            }
            result.put("payload_size", payloadSize);
        } else if (message_type == SERVER_ACK) {
            result.put("payload_size", payloadSize);
            log.trace("volcan_asr, SERVER_ACK response: {}", new Gson().toJson(result));
        } else if (message_type == SERVER_ERROR_RESPONSE) {
            // 此时 sequence 含义就是 错误码 code，payload 就是 error msg。
            payloadStr = new String(payload);
            result.put("code", sequence);
            result.put("error_msg", payloadStr);
            log.error("volcan_asr error, SERVER_ERROR_RESPONSE {}, roomId {}", new Gson().toJson(result), roomId);
        }
        return result;
    }

    /**
     * "additions": {
     * 			"emotion": "neutral",
     * 			"emotion_degree": "weak",
     * 			"emotion_degree_score": "0.9671229720115662",
     * 			"emotion_score": "0.8278537392616272",
     * 			"fixed_prefix_result": "",
     * 			"gender": "male",
     * 			"gender_score": "0.9981054067611694"
     * 		        },
     * @param utterances
     * @param msgId
     */
    private void parseUtterances(JSONArray utterances, String msgId) {
        for (int i = 0; i < utterances.size(); i++) {
            JSONObject utterance = utterances.getJSONObject(i);
            JSONObject additions = utterance.getJSONObject("additions");
            if(additions == null) {
                return;
            }
            String emotion = additions.getString("emotion");
            String emotionScore = additions.getString("emotion_score");
            if(StringUtils.isNotBlank(emotionScore)) {
                float v = Float.parseFloat(emotionScore);
                if(v >= 0.8) {
                    contextService.saveEmotion(msgId, emotion, v);
                }
            }

            String gender = additions.getString("gender");
            String genderScore = additions.getString("gender_score");
            if(StringUtils.isNotBlank(genderScore)) {
                float v = Float.parseFloat(genderScore);
                if(v >= 0.8) {
                    contextService.saveGender(msgId, gender, v);
                }
            }
            log.info("volcan asr, emotion: {}, emotionScore: {}, gender: {}, genderScore: {}, roomId {}, msgId {}", emotion, emotionScore, gender, genderScore, roomId, msgId);
        }
    }

    class PushToAsrTask implements Runnable {
        private final String msgId;
        /**
         * 是否发送数据
         */
        private boolean hasSent = false;

        public PushToAsrTask(String msgId) {
            this.msgId = msgId;
        }

        @Override
        public void run() {
            // 200ms 一个批次，故批次大小为6400, 推送音频，Bytes不应过短或过长，建议在1KB-16KB之间
            int batch_size = 2 * task.getSampleRate() * 200 / 1000;

            while (true) {
                if (!contextService.isWorking()) {
                    log.info("volcan PushToAsrTask quit, roomId {}", task.getRoomId());
                    break;
                }

                // 是否初始化完成
                boolean msgAsrReady = isMsgAsrReady(msgId);
                if (!msgAsrReady) {
                    log.debug("volcan, msgId {} asr service is not ready, skip , roomId {}", msgId, task.getRoomId());
                    try {
                        Thread.sleep(50);
                    } catch (InterruptedException e) {
                        log.error("fail to sleep");
                    }
                    continue;
                }

                VadToAsrBuffer vadToAsrBuffer = buffers.get(msgId);
                if (vadToAsrBuffer == null) {
                    try {
                        Thread.sleep(50);
                    } catch (InterruptedException e) {
                        log.error("fail to sleep");
                    }
                    continue;
                }

                // 200ms 循环一次
                try {
                    Thread.sleep(200);
                } catch (InterruptedException e) {
                    log.error("fail to sleep");
                }

                byte[] dequeue = vadToAsrBuffer.dequeue(batch_size, true);
                if (dequeue == null || dequeue.length == 0) {
                    if (!hasSent) {
                        continue;
                    } else {
                        // 结束asr会话
                        doAsr(task, msgId, ByteBuffer.allocate(10), true);
                        buffers.remove(msgId);
                        log.info("volcan PushToAsrTask, no data available, roomId {}, msgId {}", task.getRoomId(), msgId);
                        break;
                    }
                }

                log.trace("volcan PushToAsrTask , roomId {}, msgId {}, length {}", task.getRoomId(), msgId, dequeue.length);
                hasSent = true;
                doAsr(task, msgId, ByteBuffer.wrap(dequeue), false);
            }
        }
    }

}
