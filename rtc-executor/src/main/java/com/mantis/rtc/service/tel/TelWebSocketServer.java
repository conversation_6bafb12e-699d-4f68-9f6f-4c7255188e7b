package com.mantis.rtc.service.tel;

import com.mantis.rtc.service.CallContextService;
import com.mantis.rtc.service.rtc.utils.RtcConstants;
import com.mantis.rtc.service.rtc.utils.RtcUtils;
import com.mantis.rtc.utils.Utils;
import lombok.extern.slf4j.Slf4j;
import org.java_websocket.WebSocket;
import org.java_websocket.drafts.Draft;
import org.java_websocket.exceptions.InvalidDataException;
import org.java_websocket.handshake.ClientHandshake;
import org.java_websocket.handshake.ServerHandshakeBuilder;
import org.java_websocket.server.WebSocketServer;

import java.net.InetSocketAddress;
import java.net.UnknownHostException;
import java.nio.ByteBuffer;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static com.mantis.rtc.service.rtc.utils.RtcConstants.SAMPLE_RATE_8000;

@Slf4j
public class TelWebSocketServer extends WebSocketServer {
    private final ExecutorService executor = Executors.newSingleThreadExecutor();

    private CallContextService contextService = null;

    private WebSocket conn = null;


    private String getRoomId() {
        if (contextService.getTask() != null) {
            return contextService.getTask().getRoomId();
        }
        return "";
    }

    public TelWebSocketServer(int port, CallContextService contextService) throws UnknownHostException {
        super(new InetSocketAddress(port));
        this.contextService = contextService;
    }

    /**
     * 握手校验
     *
     * @param conn    The WebSocket related to this event
     * @param draft   The protocol draft the client uses to connect
     * @param request The opening http message send by the client. Can be used to access additional
     *                fields like cookies.
     * @return
     * @throws InvalidDataException
     */
    @Override
    public ServerHandshakeBuilder onWebsocketHandshakeReceivedAsServer(WebSocket conn, Draft draft,
                                                                       ClientHandshake request) throws InvalidDataException {
        return super.onWebsocketHandshakeReceivedAsServer(conn, draft, request);
    }

    /**
     * websocket 连接成功
     *
     * @param webSocket       The <code>WebSocket</code> instance this event is occurring on.
     * @param clientHandshake The handshake of the websocket instance
     */
    @Override
    public void onOpen(WebSocket webSocket, ClientHandshake clientHandshake) {
        log.info("tel websocket connect connected!, roomId {}", getRoomId());
        this.conn = webSocket;

        // 发送欢迎语
        contextService.sendWelcome();

        // 推流线程启动
        PushAudioStreamTask pushAudio = new PushAudioStreamTask();
        new Thread(pushAudio).start();
        // 通知电话接通
        contextService.getInfoReportService().reportStatus(getRoomId(), "answered", "用户通话接通");

    }

    @Override
    public void onClose(WebSocket webSocket, int i, String reason, boolean b) {
        log.error("websocket onClose, roomId {}, reason {}", getRoomId(), reason);
    }

    /**
     * 20ms 一个包，8k采样率， 因此一个包大小为 8000 * 20 /1000 * 2 = 320
     *
     * @param conn    The <code>WebSocket</code> instance this event is occurring on.
     * @param message The binary message that was received.
     */
    @Override
    public void onMessage(WebSocket conn, ByteBuffer message) {

        if (!contextService.isWorking()) {
            log.info("task is not working, exit {}", getRoomId());
            return;
        }

        byte[] payload = message.array();
        log.debug("websocket got ByteBuffer message! roomId {}, length {}", getRoomId(), payload.length);

        // 只有允许打断才入队声音
        if (!contextService.isAllowInterrupted()) {
            log.debug("call is not interrupt, skip new message, roomId {}", getRoomId());
            return;
        }

        // 欢迎语正在播放，忽略语音
        if (!contextService.isWelcomeComplete()) {
            log.trace("welcome is not complete, skip new message, roomId {}", getRoomId());
            return;
        }

        // 启动挂机程序后，不在接收语音
        if (contextService.isHookStatus()) {
            log.debug("in hook status, skip new message, roomId {}", getRoomId());
            return;
        }

        // 静默期内，不接受新的语音数据
        if(contextService.isSilence()) {
            log.debug("in silence status, skip new message, roomId {}", getRoomId());
            return;
        }

        // 强制单线程入队执行
        executor.execute(() -> {
            contextService.getRtcToVadBuffer().enqueue(payload);
            // 执行VAD 检测
            while (contextService.getRtcToVadBuffer().size() >= RtcConstants.VAD_8000_BATCH_SAMPLE_COUNT) {
                // 如果任务结束，退出
                if (!contextService.isWorking()) {
                    log.info("websocket to vad task exit, roomId {}", getRoomId());
                    break;
                }
                // 获取VAD数据
                byte[] dequeue = contextService.getRtcToVadBuffer().dequeue(RtcConstants.VAD_8000_BATCH_SAMPLE_COUNT);
                if (dequeue == null || dequeue.length == 0) {
                    continue;
                }
                // 录制
                contextService.getRecordService().recordUser(dequeue);
                // 执行vad检测
                contextService.getVadService().apply(dequeue);
            }
        });
    }

    @Override
    public void onMessage(WebSocket webSocket, String s) {
        log.info("websocket got message!, content {}", s);
    }

    @Override
    public void onError(WebSocket webSocket, Exception e) {
        log.error("websocket onError, roomId {}", getRoomId(), e);
        contextService.setFsWebsocketReady(false);

        //todo call api to alert
    }

    /**
     * websocket 服务启动
     */
    @Override
    public void onStart() {
        log.info("websocket server started!, port {}", getPort());
        setConnectionLostTimeout(0);
        contextService.setFsWebsocketReady(true);
    }

    public void clean() {
        try {
            if (conn != null) {
                conn.close();
            }
        } finally {
            this.conn = null;
        }
    }

    /**
     * 推流到websocket线程
     */
    class PushAudioStreamTask implements Runnable {
        // 60毫秒的采样数据长度
        int PACKAGE_TIME = 60;
        final int frameLength = SAMPLE_RATE_8000 * PACKAGE_TIME / 1000;
        final int channels = RtcConstants.RTC_AUDIO_CHANNELS;
        private boolean speakFlag = false;

        @Override
        public void run() {
            int batch_size = frameLength * channels * 2;

            while (true) {
                if (!contextService.isWorking()) {
                    log.info("telephone PushAudioStreamTask quit because task stop, roomId {}", getRoomId());
                    break;
                }

                if(!contextService.getHasRtcDataToPush().get()) {
                    Utils.sleep(100);
                    continue;
                }

                // 优先处理欢迎语数据
                byte[] dequeue = contextService.getWelcomeBuffer().dequeue(batch_size);
                if (dequeue == null || dequeue.length == 0) {
                    // 如果有过欢迎语数据，且当前无法获取到数据，则认为欢迎语发送完毕
                    if (contextService.isWelcomeSent() && !contextService.isWelcomeComplete()) {
                        contextService.setWelcomeComplete(true);
                    }
                    // 从正常buffer获取数据
                    dequeue = contextService.getTtsToRtcBuffer().dequeue(batch_size);
                }

                if (dequeue == null || dequeue.length == 0) {
                    // 标识推送完成
                    contextService.getHasRtcDataToPush().set(false);

                    // 标识AI说话完成
                    if(this.speakFlag) {
                        if(contextService.isHookStatus()) {
                            log.info("audio sent complete in hook status, call tel hangup, roomId {}", contextService.getTask().getRoomId());
                            Utils.sleep(100);
                            // 挂机，清理
                            contextService.hangupManual(getRoomId(), "tel", System.currentTimeMillis());
                            break;
                        }else {
                            // 追问，如果AI内容发送完毕, 开始等待访客说话
                            log.info("ai speech complete, start waiting user speak, roomId {}", getRoomId());
                            contextService.setAiSpeakEndTime(System.currentTimeMillis());
                        }
                    }
                    this.speakFlag = false;
                    Utils.sleep(PACKAGE_TIME);
                    continue;
                }

                // 有待发送数据
                log.trace("push tel, dequeue, roomId {}, length {}", getRoomId(), dequeue.length);
                this.speakFlag = true;
                contextService.getRecordService().recordAI(dequeue);
                if (conn != null) {
                    conn.send(dequeue);
                }
                Utils.sleep(PACKAGE_TIME);
            }
        }
    }
}
