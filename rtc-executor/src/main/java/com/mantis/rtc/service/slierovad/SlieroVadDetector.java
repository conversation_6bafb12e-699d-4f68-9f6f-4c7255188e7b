package com.mantis.rtc.service.slierovad;


import ai.onnxruntime.OrtException;
import lombok.extern.slf4j.Slf4j;

import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.HashMap;
import java.util.Map;

/**
 * SlieroVadDetector class for speech detection using the Sliero VAD model.
 * 注意，该对象有状态，线程不安全
 */

@Slf4j
@Deprecated
public class SlieroVadDetector {
    // OnnxModel model used for speech processing
    private final SlieroVadOnnxModel model;
    // Threshold for speech start
    private final float startThreshold;
    // Threshold for speech end
    private final float endThreshold;
    // Sampling rate
    private final int samplingRate;
    /**
     * Minimum number of silence samples to determine the end threshold of speech
     * 最小静音采样数，用于触发人声结束
     */
    private final float minSilenceSamples;
    // Additional number of samples for speech start or end to calculate speech start or end time
    private final float speechPadSamples;
    // Whether in the triggered state (i.e. whether speech is being detected)
    private boolean triggered;
    // Temporarily stored number of speech end samples
    private int tempEnd;
    // Number of samples currently being processed
    private int currentSamples;

    FileOutputStream plOutputStream = null;
    private String userDir;



    /**
     * Constructor for SlieroVadDetector, initializing the model, thresholds, sampling rate, minimum silence duration, and speech padding duration.
     * @param modelPath Path to the Sliero VAD model file
     * @param startThreshold Threshold for speech start
     * @param endThreshold Threshold for speech end
     * @param samplingRate Sampling rate of the audio data
     * @param minSilenceDurationMs Minimum duration of silence required for speech end detection, 如果静音时间超过minSilenceDurationMs，则认为说话完毕
     * @param speechPadMs Additional duration for speech startor end detection
     */
    public SlieroVadDetector(String modelPath,
                             float startThreshold,
                             float endThreshold,
                             int samplingRate,
                             int minSilenceDurationMs,
                             int speechPadMs) throws OrtException {
        // Check if the sampling rate is 8000 or 16000, if not, throw an exception
        if (samplingRate != 8000 && samplingRate != 16000) {
            throw new IllegalArgumentException("does not support sampling rates other than [8000, 16000]");
        }

        // Initialize the parameters
        this.model = new SlieroVadOnnxModel(modelPath);
        this.startThreshold = startThreshold;
        this.endThreshold = endThreshold;
        this.samplingRate = samplingRate;
        this.minSilenceSamples = samplingRate * minSilenceDurationMs / 1000f;// 16000*2000/1000=32000
        this.speechPadSamples = samplingRate * speechPadMs / 1000f;
        // Reset the state
        reset();
        userDir = System.getProperty("user.dir");
        log.info("userDir: " + userDir);
    }

    /**
     *  Method to reset the state, including the model state, trigger state, temporary end time, and current sample count
     */
    public void reset() {
        model.resetStates();
        triggered = false;
        tempEnd = 0;
        currentSamples = 0;
    }

    /**
     * apply method for processing the audio array, returning possible speech start or end times
     * @param data
     * @param returnSeconds
     * @return
     */
    public Map<String, Double> apply(byte[] data, boolean returnSeconds) {
        ByteBuffer audioFrame = ByteBuffer.wrap(data);

        // 录制访客声音，方便排查问题
        try {
            if (plOutputStream == null) {
                String playback_file_name = String.format("%s/play_%s_%d.pcm", userDir, "test", System.currentTimeMillis());
                plOutputStream = new FileOutputStream(playback_file_name);
            }
            plOutputStream.write(data);
        } catch (IOException e) {
            e.printStackTrace();
        }


        // Convert the byte array to a float array
        float[] audioData = new float[data.length / 2];
        for (int i = 0; i < audioData.length; i++) {
            audioData[i] = ((data[i * 2] & 0xff) | (data[i * 2 + 1] << 8)) / 32767.0f;
        }

        // Get the length of the audio array as the window size
        int windowSizeSamples = audioData.length;
        // Update the current sample count
        currentSamples += windowSizeSamples;

        // Call the model to get the prediction probability of speech
        float speechProb = 0;
        try {
            speechProb = model.call(new float[][]{audioData}, samplingRate)[0];
        } catch (OrtException e) {
            log.error("Error calling model: " + e.getMessage());
            throw new RuntimeException(e);
        }

//        log.info("speechProb: " + speechProb);
        // If the speech probability is greater than the threshold and the temporary end time is not 0, reset the temporary end time
        // This indicates that the speech duration has exceeded expectations and needs to recalculate the end time
        if (speechProb >= startThreshold && tempEnd != 0) {
            tempEnd = 0;
        }

        Map<String, Double> result = new HashMap<>();
        // 首次进入人声说话状态，即刚开始说话
        if (speechProb >= startThreshold && !triggered) {
            triggered = true;
        }

        // 人声说话状态内，送asr数据
        if(triggered) {
            try {
                log.info("speaking..., speechProb: {}", speechProb);
                // 持续发送给ASR识别
//                aliASR.setAudioFrame(audioFrame);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        // If the speech probability is less than a certain threshold and in the triggered state, calculate the speech end time
        // 说话状态下检测到静音
        if (speechProb < endThreshold && triggered) {
            // Initialize or update the temporary end time
            if (tempEnd == 0) {
                tempEnd = currentSamples;
            }

           // 加上允许的静音采样数之后还是静音，意味着访客说话结束
            if (currentSamples - tempEnd > minSilenceSamples) {
                tempEnd = 0;
                triggered = false;
//                String asrRes = aliASR.getAsr_res();
//                log.info("speak end, get asr result:{}", asrRes);
            }
        }
        return result;
    }

    public void close() throws OrtException {
        reset();
        model.close();

        try {
            plOutputStream.close();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
}
