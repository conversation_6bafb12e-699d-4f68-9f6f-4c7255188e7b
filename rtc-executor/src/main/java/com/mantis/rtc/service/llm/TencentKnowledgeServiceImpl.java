package com.mantis.rtc.service.llm;

// import com.mantis.micor.pojo.CallConfig;
// import com.mantis.micor.pojo.CallTask;
// import com.mantis.rtc.service.CallContextService;
// import com.mantis.rtc.service.TaskService;
//import com.tencentcloudapi.common.Credential;
//import com.tencentcloudapi.common.exception.TencentCloudSDKException;
//import com.tencentcloudapi.common.profile.ClientProfile;
//import com.tencentcloudapi.common.profile.HttpProfile;
//import com.tencentcloudapi.lkeap.v20240522.LkeapClient;
//import com.tencentcloudapi.lkeap.v20240522.models.RetrievalRecord;
//import com.tencentcloudapi.lkeap.v20240522.models.RetrievalSetting;
//import com.tencentcloudapi.lkeap.v20240522.models.RetrieveKnowledgeRequest;
//import com.tencentcloudapi.lkeap.v20240522.models.RetrieveKnowledgeResponse;
import lombok.extern.slf4j.Slf4j;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.context.annotation.Scope;
// import org.springframework.stereotype.Service;

// import java.util.Map;


/**
 * 腾讯知识库
 * @author: leo
 * @Date: 2022/3/23 14:03
 * @Description:
 */
//@Scope("singleton")
//@Service("tencentKnowledgeServiceImpl")
@Slf4j
public class TencentKnowledgeServiceImpl {
    //implements KnowledgeService, TaskService {

//    @Autowired
//    private CallContextService callContextService;
//
//    private LkeapClient client;

//    @Override
//    public boolean init(CallTask task) {
//        CallConfig.Knowledge knowledge = task.getCallConfig().getKnowledge();
//        if(knowledge == null) {
//            return true;
//        }
//
//        if(!"tencent".equalsIgnoreCase(knowledge.getVendor())) {
//            log.info("skip tecent knowledge");
//            return true;
//        }
//
//        CallConfig.TencentKn tencentKn = knowledge.getTencent();
//        if(tencentKn == null) {
//            log.error("tencent knowledge config is invalid, roomId {}", task.getRoomId());
//            return true;
//        }
//
//        Credential cred = new Credential(knowledge.getTencent().getSecretId(), knowledge.getTencent().getSecretKey());
//        // 实例化一个http选项，可选的，没有特殊需求可以跳过
//        HttpProfile httpProfile = new HttpProfile();
//        httpProfile.setEndpoint("lkeap.tencentcloudapi.com");
//        // 实例化一个client选项，可选的，没有特殊需求可以跳过
//        ClientProfile clientProfile = new ClientProfile();
//        clientProfile.setHttpProfile(httpProfile);
//        // 实例化要请求产品的client对象,clientProfile是可选的
//        client =  new LkeapClient(cred, "ap-shanghai", clientProfile);
//        return true;
//    }

//    @Override
//    public void clean() {
//        this.client = null;
//    }

//    @Override
//    public int order() {
//        return 0;
//    }

//    @Override
//    public Map<String,String> searchKnowledge(CallTask task, String query, String msgId) {
//        CallConfig.Knowledge knowledge = task.getCallConfig().getKnowledge();
//        if(knowledge == null) {
//            return null;
//        }
//
//        if(!"tencent".equalsIgnoreCase(knowledge.getVendor())) {
//            return null;
//        }
//
//        long startTime = System.currentTimeMillis();
//
//        RetrieveKnowledgeRequest req = new RetrieveKnowledgeRequest();
//        req.setKnowledgeBaseId(knowledge.getTencent().getKnowledgeBaseId());
//        req.setQuery(query);
//        req.setRetrievalMethod("SEMANTIC");
//        RetrievalSetting retrievalSetting1 = new RetrievalSetting();
//        retrievalSetting1.setTopK(3L);
//        retrievalSetting1.setType("QA");
//        retrievalSetting1.setScoreThreshold(knowledge.getTencent().getScoreLimit());
//        req.setRetrievalSetting(retrievalSetting1);
//        // 返回的resp是一个RetrieveKnowledgeResponse的实例，与请求对象对应
//        try {
//            RetrieveKnowledgeResponse resp = client.RetrieveKnowledge(req);
//            RetrievalRecord[] records = resp.getRecords();
//            if(records != null && records.length > 0) {
//                RetrievalRecord record = records[0];
//
//                callContextService.setTimerInfo(msgId, "knowledge", System.currentTimeMillis() - startTime);
//                log.info("tencent searchKnowledge, roomId {}, msgId {}, query {}, title {}, answer {}, taken: {}, perf" , task.getRoomId(), msgId, query, record.getTitle(), record.getContent(),  System.currentTimeMillis()-startTime);
//                return Map.of(record.getTitle(), record.getContent());
//            }
//        } catch (TencentCloudSDKException e) {
//            log.error("fail to query knowledge, query {}", query, e);
//            return null;
//        }
//        return null;
//    }
}
