package com.mantis.rtc.service.tts.cache;

import com.mantis.micor.pojo.CallTask;
import com.mantis.rtc.pojo.Constants;
import com.mantis.rtc.service.TaskService;
import com.mantis.rtc.utils.Utils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.mantis.rtc.pojo.Constants.*;


@Service
@Slf4j
public class TTSCacheService implements TaskService  {

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private List<TtsService> ttsServices;
    private String roomId = "";

    /**
     * 声音tts数据缓存，
     * key: query
     * value: tts数据
     */
    private final Map<String, byte[]> cache = new java.util.HashMap<>();

    public Map<String, byte[]> getCache() {
        return cache;
    }

    @Override
    public boolean init(CallTask task) {
        this.roomId = task.getRoomId();
        log.info("begin init tts cache service, roomId {}", roomId);

        // 初始化
        Set<String> targets = new HashSet<>();
        // 欢迎语
        String welcomeSpeech = task.getCallConfig().getWelcomeSpeech();
        if(welcomeSpeech.contains("$$")) {
            String[] splits = welcomeSpeech.split("\\$\\$");
            targets.addAll(Arrays.asList(splits));
        } else {
            targets.add(welcomeSpeech);
        }

        for (String str : EMPTY_ASR_RES) {
            if (StringUtils.isBlank(str)) {
                continue;
            }
            targets.add(str);
        }

        for (String str : SIMPLE_RES) {
            if (StringUtils.isBlank(str)) {
                continue;
            }
            targets.add(str);
        }

        for (String str: targets) {
            Constants.executor.submit(() -> {
                byte[] bytes = fetchTtsData(str, task);
                if (bytes != null) {
                    cache.put(str, bytes);
                }
            });
        }

        Set<String> keys = SIMPLE_RES2.keySet();
        for (String key: keys) {
            Constants.executor.submit(() -> {
                byte[] bytes = fetchTtsData(key, SIMPLE_RES2.get(key), task);
                if (bytes != null) {
                    Constants.adjustVolume(bytes, bytes.length, 0.5f);
                    cache.put(key, bytes);
                }
            });
        }

        log.info("complete init tts cache service, roomId {}", roomId);
        return true;
    }

    @Override
    public void clean() {
        cache.clear();
    }

    @Override
    public int order() {
        return 4;
    }

    /**
     * 根据指定的query，生成tts数据
     * hash 数据结构
     * @param query
     * @param task
     * @return
     */
    private byte[] fetchTtsData(String query, CallTask task) {
        String redisSubKey = query;
        // redis中， 使用md5key 代替大段中文作为redis key
        if(query.length() > 10) {
            redisSubKey = Utils.getChineseStringMD5Key(query);
        }
        if(StringUtils.isBlank(redisSubKey)) {
            log.error("fetch tts data, roomId {}, fail to calc md5 query {}", task.getRoomId(), query);
            return null;
        }
        return selectTtsData(redisSubKey, query, task);
    }

    /**
     * 根据指定的query与key， 生成tts数据
     * @param queryKey
     * @param query
     * @param task
     * @return
     */
    private byte[] fetchTtsData(String queryKey, String query, CallTask task) {
        byte[] bytes = selectTtsData(queryKey, query, task);
        if(bytes == null) {
            log.error("roomId {}, fail to fetch tts data, query key {}, query {}", task.getRoomId(), queryKey, query);
            return null;
        }
        // 取1.3 秒的数据
        int length  =  task.getSampleRate() * 500/1000 * 2 ;
        // 目标
        byte[] target = new byte[length];
        System.arraycopy(bytes, 0, target, 0, length);
        return target;
    }

    /**
     * 优先从redis中获取tts数据，如果redis中不存在，则调用tts服务获取tts数据，并放入redis中
     * @param redisSubKey
     * @param query
     * @param task
     * @return
     */
    private byte[] selectTtsData(String redisSubKey, String query, CallTask task) {
        String key = calcHashKey(task);
        HashOperations<String, String, String> hashOperations = redisTemplate.opsForHash();
        String data = hashOperations.get(key, redisSubKey);

        if (StringUtils.isBlank(data)) {
            // 缓存空，则调用tts服务
            data = tts(query, task);
            // 放redis
            if (StringUtils.isNotBlank(data)) {
                log.info("complete tts service, save into redis, roomId {}, vendor {}, query {}", task.getRoomId(), task.getCallConfig().getTts().getVendor(), query);
                redisTemplate.opsForHash().put(key, redisSubKey, data);
                redisTemplate.expire(key, 30, TimeUnit.DAYS);
            }
        } else {
            log.debug("roomId {}, get tts data from redis, query {}, key {}", task.getRoomId(), query, redisSubKey);
        }

        // base64 转为byte[]
        if (StringUtils.isNotBlank(data)) {
            return Base64.getDecoder().decode(data);
        } else {
            log.error("roomId {}, fail to select tts data, query {}, key {}", task.getRoomId(), query, redisSubKey);
            return null;
        }
    }

    private String tts(String query, CallTask task) {
        log.debug("call tts service, roomId {}, vendor {}, query {}", task.getRoomId(), task.getCallConfig().getTts().getVendor(), query);
        for (TtsService service : ttsServices) {
            if(service.accept(task)) {
                return service.tts(query, task);
            }
        }
        return null;
    }

    private static String calcHashKey(CallTask task) {
        String vendor = task.getCallConfig().getTts().getVendor();
        String channel = task.getCallConfig().getChannel();
        String voiceId = null;

        if(vendor.equalsIgnoreCase(Constants.TTS_VENDOR_VOLCAN)) {
            voiceId = task.getCallConfig().getTts().getVolcan().getVoiceId();
        } else if(vendor.equalsIgnoreCase(Constants.TTS_VENDOR_COSYVOICE)) {
            voiceId = task.getCallConfig().getTts().getCosyvoice().getVoiceId();
        }else if(vendor.equalsIgnoreCase(Constants.TTS_VENDOR_MINIMAX)) {
            voiceId = task.getCallConfig().getTts().getMinimax().getVoiceId();
        }
        return String.format("tts:%s:%s:%s", vendor, voiceId, channel);
    }

}
