package com.mantis.rtc.service.tts.cosyvoice.stream;

import com.alibaba.dashscope.audio.tts.SpeechSynthesisResult;
import com.alibaba.dashscope.common.ResultCallback;
import com.alibaba.dashscope.common.Status;
import com.mantis.rtc.service.CallContextService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.nio.ByteBuffer;

/**
 * TTS 回调处理
 */
@Slf4j
public class CosyVoiceResultCallback extends ResultCallback<SpeechSynthesisResult> {
    private CallContextService contextService = null;
    private String roomId = null;
    private String msgId = null;

    private boolean isSessionFirstReply = true;

    public CosyVoiceResultCallback(CallContextService contextService, String roomId, String msgId) {
        super();
        this.contextService = contextService;
        this.roomId = roomId;
        this.msgId = msgId;
    }

    @Override
    public void onOpen(Status status) {
        log.info("tts onOpen {}, roomId {} msgId {}", status, roomId, msgId);
    }

    @Override
    public void onEvent(SpeechSynthesisResult result) {
        ByteBuffer audioFrame = result.getAudioFrame();
        if(audioFrame == null) {
            return;
        }

        byte[] tts = audioFrame.array();
        int remaining = tts.length;

        if(remaining == 0) {
            log.error("invalid audioFrame, remaining is zero, roomId {}, msgId {}", roomId, msgId);
            return;
        }

        if(StringUtils.isNotBlank(msgId) && msgId.contains("welcome")) {
            log.debug("on tts data of welcome, roomId {}, msgId {}, size {}", roomId, msgId, remaining);
            contextService.getWelcomeBuffer().enqueue(tts);
            contextService.setWelcomeSent(true);
        } else {
            if(isSessionFirstReply) {
                log.info("on first tts data, roomId {}, msgId {}, size {}, perf", roomId, msgId, remaining);
                isSessionFirstReply = false;
                contextService.setMsgInfoTrack(msgId, "tts_first_token", System.currentTimeMillis());
                //AI 开始说话了，需要退出用户静默阶段，允许打断
                contextService.setSilence(false, "tts_stream_begin", msgId);
            }
            // 如果当前消息没有被打断，则写入rtc buffer
            if(!contextService.isInterrupted(msgId)) {
                log.debug("on tts data, roomId {}, msgId {}, size {}", roomId, msgId, remaining);
                // 存入缓存
                contextService.getTtsToRtcBuffer().enqueue(tts, msgId);
            } else {
                log.info("on tts data interrupted, roomId {}, msgId {}", roomId, msgId);
            }
        }
    }

    @Override
    public void onComplete() {
        log.debug("on tts complete, roomId {}, msgId {}", roomId, msgId);
    }

    /**
     * [2025-01-23 23:18:10,471][OkHttp https://dashscope.aliyuncs.com/...][][ERROR][com.mantis.rtc.service.tts.cosyvoice.stream.CosyVoiceResultCallback] tts error, roomId ROOM_50012_2021102414550087640001, message {"statusCode":44,"message":"request timeout after 23 seconds.","code":"InvalidParameter","isJson":true,"requestId":"18bcd83e-d082-49da-9983-4d9b7da6726e"}
     * @param e The exception instance.
     */
    @Override
    public void onError(Exception e) {
        log.error("tts error, roomId {}, message {}", roomId, e.getMessage());
    }
}
