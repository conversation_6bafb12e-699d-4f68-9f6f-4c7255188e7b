package com.mantis.rtc.service.agora;

import com.mantis.rtc.service.CallContextService;
import io.agora.rtc.AgoraRtcConn;
import io.agora.rtc.DefaultRtcConnObserver;
import io.agora.rtc.RtcConnInfo;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class AgoraDefaultRtcConnObserver extends DefaultRtcConnObserver {
    private final CallContextService contextService;

    public AgoraDefaultRtcConnObserver(CallContextService contextService) {
        this.contextService = contextService;
    }

    @Deprecated
    public void onConnected(AgoraRtcConn agoraRtcConn, RtcConnInfo connInfo, int reason) {
        log.info("onConnected agoraRtcConn={}, connInfo={}, reason={}", agoraRtcConn, connInfo, reason);
    }

    /**
     * 远端用户加入频道时触发。
     * 你可以在该回调中获取远端用户 ID。
     * @param agoraRtcConn
     * @param userId
     */
    @Override
    public void onUserJoined(AgoraRtcConn agoraRtcConn, String userId) {
        log.info("remote onUserJoined: uid {}, roomId {}, will send welcome", userId, contextService.getTask().getRoomId());
        // 发送欢迎语
        contextService.sendWelcome();
        contextService.getInfoReportService().reportStatus(contextService.getTask().getRoomId(), "answered", "用户通话接通");
    }

    /**
     * 远端用户离开频道时触发。
     * 你可以通过 reason 参数获取该用户离开频道的原因。
     * @param agoraRtcConn
     * @param userId
     * @param reason
     */
    @Override
    public void onUserLeft(AgoraRtcConn agoraRtcConn, String userId, int reason) {
        log.info("remote onUserLeave: uid {}, reason {}, roomId {}, reason {}", userId, reason, contextService.getTask().getRoomId(), reason);
        contextService.hangupManual(contextService.getTask().getRoomId(), "rtc", System.currentTimeMillis());
    }

}
