package com.mantis.rtc.service.llm;

import com.alibaba.fastjson.JSON;
import com.mantis.rtc.pojo.chat.*;
import com.mantis.rtc.service.CallContextService;
import lombok.Getter;
import lombok.Setter;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Response;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import org.apache.commons.lang3.StringUtils;

import java.util.List;
import java.util.Objects;
import java.util.function.Consumer;

/**
 * EventSource listener for chat-related events.
 *
 * <AUTHOR>
 */
@Slf4j
public abstract class AbstractStreamListener extends EventSourceListener {
    private boolean isFirst = true;
    /**
     * 本轮对话的一些统计信息
     */
    protected ChatCounter chatCounter = new ChatCounter();
    protected String roomId;

    protected String msgId;

    /**
     * 当前的用户消息
     */
    protected String useMsg;

    /**
     * 完整拼接后的回复，注意会包含控制字符串
     */
    protected StringBuilder msgBuilder = new StringBuilder();

    protected ChatContextServiceMemoryImpl chatContextService;
    protected CallContextService contextService;

    /**
     * Called when all new message are received.
     *
     * @param message the new message
     */
    @Setter
    @Getter
    protected Consumer<String> onComplete = s -> {

    };

    public AbstractStreamListener(ChatContextServiceMemoryImpl chatContextService, CallContextService contextService, String roomId, String msgId, String userMsg) {
        this.roomId = roomId;
        this.useMsg = userMsg;
        this.msgId = msgId;
        this.chatContextService = chatContextService;
        this.contextService = contextService;
    }

    /**
     * Called when a new message is received.
     * 收到消息 trunk
     *
     * @param message the new message
     */
    public abstract void onMsg(String message);

    /**
     * 处理usage
     *
     * @param usage
     */
    public void onUsage(Usage usage) {
        log.debug("llm usage, roomId: {}, useMsg: {}, usage: {}", roomId, useMsg, usage);
        chatCounter.setUsage(usage);
    }

    /**
     * Called when an error occurs.
     * 出错时调用
     *
     * @param throwable the throwable that caused the error
     * @param response  the response associated with the error, if any
     */
    public abstract void onError(Throwable throwable, String response);

    @Override
    public void onOpen(EventSource eventSource, Response response) {
        log.debug("roomId {}, okhttp connection onOpen", roomId);
    }

    @Override
    public void onClosed(EventSource eventSource) {
        log.debug("roomId {}, okhttp connection onClosed", roomId);
    }


    @Override
    public void onEvent(EventSource eventSource, String id, String type, String data) {
        Boolean isResponseAPI = contextService.getTask().getCallConfig().getLlm().getResponse();
        if(Boolean.TRUE.equals(isResponseAPI)) {
            // 处理response API
            handleResponseApi(type, data);
        } else {
            handleChatApi(data);
        }
    }

    /**
     * OPENAI Response API
     * llm response style, id null, type response.created, data {"type":"response.created","response":{"created_at":1752982069,"id":"resp_02175298206942527857cd728c783ddf90d74b61664d1222011d2","max_output_tokens":32768,"model":"doubao-seed-1-6-flash-250615","object":"response","previous_response_id":"resp_0217529396065984225c495f9ba9202d9c7a7bd20f6f92fea5a84","thinking":{"type":"disabled"},"service_tier":"default","store":true},"sequence_number":0}
     * llm response style, id null, type response.output_text.delta, data {"type":"response.output_text.delta","content_index":0,"delta":"在","item_id":"msg_02175298206961800000000000000000000ffffac15585b7d8512","output_index":0,"sequence_number":4}
     * .....
     * llm response style, id null, type response.output_text.done, data {"type":"response.output_text.done","content_index":0,"item_id":"msg_02175298206961800000000000000000000ffffac15585b7d8512","output_index":0,"text":"在呢同学，我是您的课程助理晓敏，看您今天还没听课，今天课程是中医按摩手法，能解决很多身体问题，链接已发微信，有空看看。","sequence_number":45}
     * llm response style, id null, type response.completed, data {"type":"response.completed","response":{"created_at":1752982069,"id":"resp_02175298206942527857cd728c783ddf90d74b61664d1222011d2","max_output_tokens":32768,"model":"doubao-seed-1-6-flash-250615","object":"response","output":[{"type":"message","role":"assistant","content":[{"type":"output_text","text":"在呢同学，我是您的课程助理晓敏，看您今天还没听课，今天课程是中医按摩手法，能解决很多身体问题，链接已发微信，有空看看。"}],"status":"completed","id":"msg_02175298206961800000000000000000000ffffac15585b7d8512"}],"previous_response_id":"resp_0217529396065984225c495f9ba9202d9c7a7bd20f6f92fea5a84","thinking":{"type":"disabled"},"service_tier":"default","status":"completed","usage":{"input_tokens":1657,"output_tokens":41,"total_tokens":1698,"input_tokens_details":{"cached_tokens":1561},"output_tokens_details":{"reasoning_tokens":0}},"store":true},"sequence_number":48}
     * llm response style, id null, type null, data [DONE]
     * @param type
     * @param data
     */
    private void handleResponseApi(String type, String data) {
        log.debug("llm response api event: type {}, data {}", type, data);
        if (data.equals("[DONE]")) {
            return;
        }

        // llm回复结束, 可以获取到全部文本
        if("response.output_text.done".equalsIgnoreCase(type)) {
            onComplete.accept(msgBuilder.toString());
            contextService.getTtsService().completeSession(msgId);
            return;
        }

        // 回复输出完毕，可以获取token消耗
        if("response.completed".equalsIgnoreCase(type)) {
            ResponseComplete responseComplete = JSON.parseObject(data, ResponseComplete.class);
            ResponseComplete.ResponseUsage usage = responseComplete.getResponse().getUsage();
            log.info("llm response completed, roomId {}, msgId {}, token usage {}, time taken {} s", roomId, msgId, usage, System.currentTimeMillis()/1000 - responseComplete.getResponse().getCreated_at());
            return;
        }

        if(!contextService.isWorking()) {
            return;
        }

        // 如果不是当前msgId，则跳过
        if(!contextService.isCurrentMsgId(msgId)) {
            contextService.getTtsService().completeSession(msgId);
            log.debug("llm onEvent skip, because isCurrentMsgId, roomId {}, msgId {}, currentMsgId {}", roomId, msgId, contextService.getCurrentMsgId());
            return;
        }

        if(contextService.isInterrupted(msgId)) {
            log.debug("llm onEvent skip, because interrupted, roomId {}, msgId {}", roomId, msgId);
            return;
        }

        // 大模型即将开始输出，开始tts连接
        if("response.output_item.added".equalsIgnoreCase(type)) {
            log.info("got llm response ready from llm: {}, roomId {}, msgId {}", data, roomId, msgId);
            contextService.getTtsService().onChatStreamStart(roomId, msgId);
            return;
        }

        // 处理delta，获取流式回复
        if("response.output_text.delta".equalsIgnoreCase(type)) {
            if(isFirst) {
                isFirst = false;
                log.info("got first response from llm: {}, roomId {}, msgId {}, perf", data, roomId, msgId);
                contextService.setMsgInfoTrack(msgId, "llm_first_token", System.currentTimeMillis());
            }

            ResponseDelta delta = JSON.parseObject(data, ResponseDelta.class);
            String content = delta.getDelta();

            if (StringUtils.isNotBlank(content)) {
                msgBuilder.append(content);
                log.debug("roomId {}, msgId {}, got response from llm: {} ", roomId, msgId, msgBuilder.toString());
                // 将AI消息写入redis
                chatContextService.onAIMsgTrunk(msgId, msgBuilder.toString());
            }

            // 过滤掉控制字符，写回
            String filter = filter(content);
            if (StringUtils.isNotBlank(filter)) {
                onMsg(filter);
            }
        }
    }

    /**
     * openai chat api
     * @param data
     */
    private void handleChatApi(String data) {
        if (data.equals("[DONE]")) {
            onComplete.accept(msgBuilder.toString());
            return;
        }

        ChatCompletionResponse response = JSON.parseObject(data, ChatCompletionResponse.class);
        // 计算usage
        Usage usage = response.getUsage();
        if (usage != null) {
            onUsage(usage);
        }

        if(!contextService.isWorking()) {
            return;
        }

        // 如果不是当前msgId，则跳过
        if(!contextService.isCurrentMsgId(msgId)) {
            contextService.getTtsService().completeSession(msgId);
            log.debug("llm onEvent skip, because isCurrentMsgId, roomId {}, msgId {}, currentMsgId {}", roomId, msgId, contextService.getCurrentMsgId());
            return;
        }

        if(contextService.isInterrupted(msgId)) {
            log.debug("llm onEvent skip, because interrupted, roomId {}, msgId {}", roomId, msgId);
            return;
        }

        // 读取Json
        List<ChatChoice> choices = response.getChoices();
        if (choices == null || choices.isEmpty()) {
            return;
        }

        // 如果第一次收到消息，则开始tts连接
        if(isFirst) {
            isFirst = false;
            log.info("got first response from llm: {}, roomId {}, msgId {}, perf", data, roomId, msgId);
            contextService.setMsgInfoTrack(msgId, "llm_first_token", System.currentTimeMillis());
            contextService.getTtsService().onChatStreamStart(roomId, msgId);
        }

        for(ChatChoice choice : choices) {
            // 如果llm结束
            String finishReason = choice.getFinishReason();
            if(StringUtils.isNotBlank(finishReason) && finishReason.equalsIgnoreCase("stop")) {
                contextService.getTtsService().completeSession(msgId);
                return;
            }

            Message delta = choice.getDelta();
            // raw response
            String content = delta.getContent();
            if (StringUtils.isNotBlank(content)) {
                msgBuilder.append(content);
                log.debug("roomId {}, msgId {}, got response from llm: {} ", roomId, msgId, msgBuilder.toString());
                // 将AI消息写入redis
                chatContextService.onAIMsgTrunk(msgId, msgBuilder.toString());
            }

            // 过滤掉控制字符，写回
            String filter = filter(content);
            if (StringUtils.isNotBlank(filter)) {
                onMsg(filter);
            }
        }
    }

    /**
     * 过滤方括号内的内容，注意是流式的场景，content只是文本片段，处理会更复杂一些，需要借助一个全局对象
     *
     * @param content AI的回复内容
     * @return
     */
    protected String filter(String content) {
        if (StringUtils.isBlank(content)) {
            return null;
        }

        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < content.length(); i++) {
            if (content.charAt(i) == '[' || content.charAt(i) == '【' || content.charAt(i) == '{' || content.charAt(i) == '<') {
                chatCounter.incrBucketCount();
                continue;
            }

            if (content.charAt(i) == ']' || content.charAt(i) == '】' || content.charAt(i) == '}' || content.charAt(i) == '>') {
                chatCounter.decrBucketCount();
                continue;
            }

            if (chatCounter.isFilterMode()) {
                continue;
            }
            sb.append(content.charAt(i));
        }

        return sb.toString();
    }

    @SneakyThrows
    @Override
    public void onFailure(EventSource eventSource, Throwable throwable, Response response) {
        try {
            log.error("roomId {}, Stream connection", roomId, throwable);
            String responseText = "";

            if (Objects.nonNull(response)) {
                responseText = response.body().string();
            }

            log.error("roomId {}, response：{}", roomId, responseText);

            String forbiddenText = "Your access was terminated due to violation of our policies";

            if (StringUtils.contains(responseText, forbiddenText)) {
                log.error("Chat session has been terminated due to policy violation");
                log.error("检测到号被封了");
            }

            String overloadedText = "That model is currently overloaded with other requests.";

            if (StringUtils.contains(responseText, overloadedText)) {
                log.error("检测到官方超载了，赶紧优化你的代码，做重试吧");
            }

            this.onError(throwable, responseText);

        } catch (Exception e) {
            log.warn("onFailure error", e);
        } finally {
            eventSource.cancel();
        }
    }
}
