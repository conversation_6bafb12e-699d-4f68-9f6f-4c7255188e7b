package com.mantis.rtc.service.tts.minimax.stream;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.gson.JsonObject;
import com.mantis.micor.pojo.CallConfig;
import com.mantis.micor.pojo.CallTask;
import com.mantis.rtc.pojo.Constants;
import com.mantis.rtc.service.CallContextService;
import com.mantis.rtc.service.TaskService;
import com.mantis.rtc.service.tts.TTSStreamService;
import com.mantis.rtc.service.tts.cache.TTSCacheService;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.codec.DecoderException;
import org.apache.commons.codec.binary.Hex;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Queue;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Service
@Slf4j
public class MinimaxTTSStreamService implements TaskService, TTSStreamService {
    // 同步发送与接受的锁
    private final Lock lock = new ReentrantLock();
    private final ExecutorService executor = Executors.newSingleThreadExecutor();


    @Autowired
    @Qualifier("volTTSWebsocketClient")
    private OkHttpClient okHttpClient;

    @Autowired
    private CallContextService contextService;

    @Autowired
    private TTSCacheService ttsCacheService;

    /**
     * websocket 连接池，用于缓存连接
     */
    private final Queue<WebSocket> queue = new ArrayBlockingQueue<>(10);

    /**
     * msgId与对应使用的websocket映射
     */
    private final Map<String, WebSocket> msgIdWebSocketMap = new ConcurrentHashMap<>();
    /**
     * websocket与msgId映射
     */
    private final Map<WebSocket, String> webSocketMsgIdMap = new ConcurrentHashMap<>();
    private CallTask task = null;
    private String roomId = "";

    private CallConfig.MinimaxTTS tts = null;

    private static final int MAX_POOL_SIZE = 3;

    // 缓冲区，用于流式文本的攒句
    private Map<String, SentenceBuffer> sentenceBufferMap = new ConcurrentHashMap<>();

    @Override
    public void call(String sentence, String roomId, String msgId, boolean complete) {
        if (StringUtils.isBlank(sentence)) {
            contextService.setSilence(false, "empty query in tts", msgId);
            return;
        }

        // 直接从内存中获取tts数据
        byte[] bytes = ttsCacheService.getCache().get(sentence);

        if (bytes != null) {
            if (StringUtils.isNotBlank(msgId) && msgId.contains("welcome")) {
                log.info("on minimax tts data of welcome by cache, roomId {}, msgId {}, msg {}", roomId, msgId, sentence);
                contextService.getWelcomeBuffer().enqueue(bytes);
                if(complete) {
                    contextService.setWelcomeSent(true);
                }
            } else {
                log.info("on minimax tts data by cache of sentence {}, roomId {}, msgId {}, size {}", sentence, roomId, msgId, bytes.length);
                contextService.getTtsToRtcBuffer().enqueue(bytes, msgId);
                contextService.setSilence(false, "complete minimax tts by cache", msgId);
            }
            return;
        }

        log.info("fail to get audio from cache, call minimax tts stream service, send text {} to tts, roomId {}, msgId {}", sentence, roomId, msgId);

        WebSocket webSocket = fetchWebsocketFromQueue(msgId);
        if (webSocket == null) {
            log.error("minimax tts websocket is null, roomId {}, skip", roomId);
            contextService.setSilence(false, "no websocket in minimax tts", msgId);
            return;
        }

        try {
            startTTSSession(webSocket, msgId);
            sendMessage(webSocket, tts.getVoiceId(), msgId, sentence);
        } finally {
            completeSession(msgId);
        }
    }

    @Override
    public void onChatStreamStart(String roomId, String msgId) {
        log.info("minimax tts sent stream start, roomId {}, msgId {}", roomId, msgId);
        WebSocket webSocket = fetchWebsocketFromQueue(msgId);
        if (webSocket == null) {
            log.error("minimax tts websocket is null, roomId {}, skip", roomId);
            return;
        }
        startTTSSession(webSocket, msgId);
    }

    @Override
    public void streamingCall(String sentence, String msgId) {
        String msgTosent = sentence;
        if (StringUtils.isBlank(sentence)) {
            return;
        }
        if (contextService.isInterrupted(msgId)) {
            log.info("tts streamingCall skip, because interrupted, roomId {}", roomId);
            return;
        }

        if (!contextService.isCurrentMsgId(msgId)) {
            log.info("tts streamingCall skip, because isCurrentMsgId, roomId {}, msgId {}, currentMsgId {}", roomId, msgId, contextService.getCurrentMsgId());
            return;
        }

        // 初始化缓冲区
        SentenceBuffer buffer = sentenceBufferMap.get(msgId);
        if(buffer == null) {
            buffer = new SentenceBuffer();
            sentenceBufferMap.put(msgId, buffer);
        }

        // 如果缓冲区未被标记为已缓冲，则将句子添加到缓冲区
        if(!buffer.isBuffered()) {
            buffer.getSentenceBuffer().append(sentence);
            // 如果缓冲区长度小于20个字符，直接返回
            if(buffer.getSentenceBuffer().length() < 20) {
                log.debug("minimax tts buffer length less than 20, skip streaming, roomId {}, msgId {}, buffer: {}", roomId, msgId, buffer.getSentenceBuffer().toString());
                return;
            }

            // 如果缓冲区长度大于20个字符，则将缓冲区标识为已缓冲，后续不再添加句子
            buffer.setBuffered(true);
            msgTosent = buffer.getSentenceBuffer().toString();
        } 
        

        WebSocket webSocket = msgIdWebSocketMap.get(msgId);
        if (webSocket == null) {
            log.error("streamingCall, fail to fetch websocket by msgId {}", msgId);
            return;
        }

        log.info("minimax tts streamingCall, roomId {}, msgId {}, sentence: {}, thread {}", roomId, msgId, msgTosent, Thread.currentThread().getName());
        sendMessage(webSocket, tts.getVoiceId(), msgId, msgTosent);
    }

    @Override
    public void completeSession(String msgId) {
        log.debug("try to complete minimax tts session, roomId {}, msgId {}", roomId, msgId);
        SentenceBuffer buffer = sentenceBufferMap.get(msgId);

        if(buffer.getSentenceBuffer() != null && buffer.getSentenceBuffer().length() > 0) {
            log.debug("minimax tts completeSession, roomId {}, msgId {}, buffer: {}", roomId, msgId, buffer.getSentenceBuffer().toString());
            streamingCall(buffer.getSentenceBuffer().toString(), msgId);
        }

        WebSocket webSocket = msgIdWebSocketMap.get(msgId);
        if (webSocket == null) {
            log.debug("fail to got websocket by msgId {}", msgId);
            return;
        }
        finishSession(webSocket, msgId);
    }

    @Override
    public boolean init(CallTask task) {
        this.task = task;
        this.roomId = task.getRoomId();
        msgIdWebSocketMap.clear();
        webSocketMsgIdMap.clear();
        sentenceBufferMap.clear();

        String vendor = task.getCallConfig().getTts().getVendor();
        if (!vendor.equalsIgnoreCase(Constants.TTS_VENDOR_MINIMAX)) {
            return true;
        }

        this.tts = task.getCallConfig().getTts().getMinimax();
        if (tts == null) {
            log.error("tts config is invalid, roomId {}", roomId);
            return false;
        }

        if (StringUtils.isBlank(tts.getApiKey())) {
            log.error("tts apiKey is invalid, roomId {}", roomId);
            return false;
        }

        if (StringUtils.isBlank(tts.getVoiceId())) {
            log.error("tts voiceId is invalid, roomId {}", roomId);
            return false;
        }

        log.info("begin init minimax tts stream service, roomId {}", task.getRoomId());
        initWebsocketPool(roomId);
        return true;
    }


    private WebSocket fetchWebsocketFromQueue(String msgId) {
        if (msgIdWebSocketMap.containsKey(msgId)) {
            return msgIdWebSocketMap.get(msgId);
        }

        initWebsocketPool(roomId);

        for (int i = 0; i < 4; i++) {
            WebSocket webSocket = queue.poll();
            if (webSocket != null) {
                // 与msgId 建立映射关系
                msgIdWebSocketMap.put(msgId, webSocket);
                webSocketMsgIdMap.put(webSocket, msgId);
                log.info("fetch minimax tts websocket success, msgId {}, roomId {}, left {}", msgId, task.getRoomId(), queue.size());
                return webSocket;
            }

            log.info("fail to get minimax tts websocket, msgId {}, will retry {}, roomId {}", msgId, i + 1, task.getRoomId());
            try {
                Thread.sleep(10);
            } catch (InterruptedException e) {
                log.error("fail to sleep", e);
            }
        }

        log.error("fail to get minimax tts websocket and retry, msgId {}, roomId {}", msgId, task.getRoomId());
        return null;
    }

    @Override
    public void clean() {
        msgIdWebSocketMap.clear();
        webSocketMsgIdMap.clear();
        sentenceBufferMap.clear();
        while (true) {
            WebSocket client = queue.poll();
            if (client == null) {
                break;
            }
            try {
                client.close(1000, "clean");
            } catch (Exception e) {
                log.error("fail to clean tts websocket", e);
            }
        }
    }

    private void initWebsocketPool(String roomId) {
        for (int i = 0; i < MAX_POOL_SIZE - queue.size(); i++) {
            Constants.ASR_TTS_EXECUTOR.submit(() -> connectTts(roomId));
        }
    }

    private void connectTts(String roomId) {
        log.info("begin connect minimax tts, roomId {}, current queue size {}", roomId, queue.size());
        final Request request = new Request.Builder()
                .url("wss://api.minimax.chat/ws/v1/t2a_v2")
                .header("Authorization", "Bearer " + tts.getApiKey())
                .build();

        okHttpClient.newWebSocket(request, new WebSocketListener() {
            private boolean isSessionFirstReply = true;

            @Override
            public void onOpen(WebSocket webSocket, Response response) {
//                log.info("minimax tts websocket open, roomId {}, queue size {}", roomId, queue.size());
            }

            @Override
            public void onMessage(WebSocket webSocket, String text) {
                if (!contextService.isWorking()) {
                    return;
                }

                if (StringUtils.isBlank(text)) {
                    log.error("minimax tts onMessage, text is blank, roomId {}", roomId);
                    return;
                }

                JSONObject jsonObject = null;
                try {
                    jsonObject = JSON.parseObject(text);
                } catch (Exception e) {
                    log.error("minimax tts onMessage, invalid json, roomId {}, text is null", roomId);
                    return;
                }

                if (jsonObject == null) {
                    log.error("minimax tts onMessage, fail to parse json, roomId {}, text is null", roomId);
                    return;
                }

                String type = jsonObject.getString("event");
                if ("connected_success".equals(type)) {
                    log.info("minimax tts connected success, roomId {}, queue size {}", roomId, queue.size());
                    queue.add(webSocket);
                    return;
                }

                if ("task_started".equals(type)) {
                    log.info("minimax tts task started, roomId {}", roomId);
                    return;
                }

                // 获取msgId
                String msgId = webSocketMsgIdMap.get(webSocket);
                if(StringUtils.isBlank(msgId)) {
                    log.error("fail to get msgId by minimax tts websocket, roomId {}", roomId);
                    return;
                }

                JSONObject data = jsonObject.getJSONObject("data");
                if (data != null && data.containsKey("audio")) {
                    String audioData = data.getString("audio");
                    // Hex解码音频数据
                    byte[] audioBytes = null;
                    try {
                        audioBytes = Hex.decodeHex(audioData);
                    } catch (DecoderException e) {
                        throw new RuntimeException(e);
                    }

                    log.debug("minimax tts audio, roomId {}, is_final: {}, trace_id {}", roomId, data.getBoolean("is_final"), data.getString("trace_id"));

                    if (StringUtils.isNotBlank(msgId) && msgId.contains("welcome")) {
                        log.info("on minimax tts data of welcome, roomId {}, msgId {}, size {}", roomId, msgId, audioData.length());
                        contextService.getWelcomeBuffer().enqueue(audioBytes);
                        contextService.setWelcomeSent(true);
                    } else {
                        if (isSessionFirstReply) {
                            log.info("on first minimax tts data, roomId {}, msgId {}, size {}", roomId, msgId, audioData.length());
                            isSessionFirstReply = false;
                            contextService.setMsgInfoTrack(msgId, "tts_first_token", System.currentTimeMillis());
                            contextService.setSilence(false, "tts_stream_begin", msgId);
                        }
                        if (!contextService.isInterrupted(msgId)) {
                            log.info("on minimax tts data, roomId {}, msgId {}, size {}", roomId, msgId, audioData.length());
                            contextService.getTtsToRtcBuffer().enqueue(audioBytes, msgId);
                        } else {
                            log.info("on minimax tts data interrupted, roomId {}, msgId {}", roomId, msgId);
                            webSocket.close(1000, "interrupted");
                        }
                    }
                    return;
                }

                if ("task_finished".equals(type)) {
                    log.info("minimax tts task finished, roomId {}", roomId);
                    msgIdWebSocketMap.values().removeIf(ws -> ws == webSocket);
                    webSocketMsgIdMap.remove(webSocket);
                    return;
                }

                if ("task_failed".equals(type)) {
                    log.error("minimax tts task error, roomId {}, error: {}", roomId, text);
                    // 释放websocket
                    webSocket.close(1000, "task_failed");
                    return;
                }
            }

            @Override
            public void onClosed(WebSocket webSocket, int code, String reason) {
                String msgId = webSocketMsgIdMap.get(webSocket);
                log.info("minimax tts websocket closed, roomId {}, msgId {}, reason {}", roomId, msgId, reason);
                msgIdWebSocketMap.values().removeIf(ws -> ws == webSocket);
                webSocketMsgIdMap.remove(webSocket);

            }

            @Override
            public void onFailure(WebSocket webSocket, Throwable t, Response response) {
                String msgId = webSocketMsgIdMap.get(webSocket);
                log.error("minimax tts websocket failure, roomId {}, msgId {}, error: {}", roomId, msgId, t.getMessage());
                msgIdWebSocketMap.values().removeIf(ws -> ws == webSocket);
                webSocketMsgIdMap.remove(webSocket);
            }
        });
    }


    private void sendMessage(WebSocket webSocket, String voiceId, String sessionId, String text) {
        executor.submit(new Runnable() {
            @Override
            public void run() {
                JsonObject request = new JsonObject();
                request.addProperty("event", "task_continue");
                request.addProperty("text", text);
                webSocket.send(request.toString());
            }
        });
    }

    private boolean startTTSSession(WebSocket webSocket, String msgId) {
        log.info("start minimax tts session, roomId {}, msgId {}", roomId, msgId);
        JsonObject request = new JsonObject();
        request.addProperty("event", "task_start");
        request.addProperty("model", "speech-02-hd");
        request.addProperty("language_boost", "Chinese");

        JsonObject voiceSetting = new JsonObject();
        voiceSetting.addProperty("voice_id", tts.getVoiceId());
        voiceSetting.addProperty("speed", 1);
        voiceSetting.addProperty("vol", 1);
        voiceSetting.addProperty("pitch", 0);
        voiceSetting.addProperty("emotion", "neutral");

        JsonObject audioSetting = new JsonObject();
        audioSetting.addProperty("sample_rate", task.getSampleRate());
        audioSetting.addProperty("format", "pcm");
        audioSetting.addProperty("channel", 1);

        request.add("voice_setting", voiceSetting);
        request.add("audio_setting", audioSetting);

        return webSocket.send(request.toString());
    }

    private boolean finishSession(WebSocket webSocket, String sessionId) {
        log.debug("finish minimax tts session, roomId {}, sessionId {}", roomId, sessionId);
        JsonObject request = new JsonObject();
        request.addProperty("event", "task_finish");
        return webSocket.send(request.toString());
    }

    @Override
    public int order() {
        return 3;
    }

    @Data
    class SentenceBuffer {
        private StringBuilder sentenceBuffer = new StringBuilder();
        private boolean isBuffered = false;

        public void setBuffered(boolean isBuffered) {
            this.isBuffered = isBuffered;
        }

        public boolean isBuffered() {
            return isBuffered;
        }
        // public String append(String sentence) {
        //     sentenceBuffer.append(sentence);

        //     // 如果遇到标点符号，则将缓冲区内容发送给minimax
        //     if(sentence.matches(",，.*[。！？]$")) {
        //         String bufferedText = sentenceBuffer.toString();
        //         sentenceBuffer.setLength(0);
        //         return bufferedText;
        //     }

        //     return null;
        // }
    }
} 