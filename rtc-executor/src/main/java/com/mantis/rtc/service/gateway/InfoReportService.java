package com.mantis.rtc.service.gateway;

import com.alibaba.fastjson.JSON;
import com.mantis.micor.pojo.CallConfig;
import com.mantis.micor.pojo.CallStop;
import com.mantis.micor.pojo.ExecutorInfo;
import com.mantis.rtc.config.OkhttpClientConfig;
import com.mantis.rtc.pojo.Constants;
import com.mantis.rtc.pojo.chat.TaskStatus;
import com.mantis.rtc.service.CallContextService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.io.IOException;

import static com.mantis.rtc.pojo.Constants.JSON_MEDIA_TYPE;

/**
 * 信息上报模块
 */
@Slf4j
@Service
public class InfoReportService {

    @Autowired
    @Qualifier("okhttpClient")
    private OkHttpClient client;

    @Autowired
    private OkhttpClientConfig config;

    @Autowired
    private CallContextService contextService;

    @Scheduled(fixedDelay = 5000)
    public void reportInfo(){
        if(!contextService.isFsWebsocketReady()) {
            log.error("fs websocket not start!!!, port {}", contextService.getExecutorInfo().getTelPort());
            return;
        }
        ExecutorInfo executorInfo = contextService.getExecutorInfo();
        if (contextService.getTask() != null) {
            executorInfo.setRoomId(contextService.getTask().getRoomId());
        }
        executorInfo.setTimestamp(System.currentTimeMillis());
        executorInfo.setStatus(contextService.isWorking() ? "BUSY" : "IDLE");
        log.debug("report info {}", executorInfo);

        RequestBody body = RequestBody.create(JSON_MEDIA_TYPE, JSON.toJSONString(executorInfo));

        Request request = new Request.Builder()
                .url(config.getUrl() + "/executor/info")
                .post(body)
                .build();

        try (Response response = client.newCall(request).execute()) {
            if(!response.isSuccessful()) {
                log.error("fail to upload info, url {}, response {}", config.getUrl(), response.message());
            }
        }catch (IOException e) {
            log.error("exception when upload info, url {}", config.getUrl(), e);
        }
    }

//    public void removeRtcRoomIdExecutorInfoMapping(String roomId){
//        log.info("reportRtcClean roomId {}", roomId);
//
//        Request request = new Request.Builder()
//                .url(config.getUrl() + "/executor/rtcClean/" + roomId)
//                .get()
//                .build();
//
//        try (Response response = client.newCall(request).execute()) {
//            if(!response.isSuccessful()) {
//                log.error("fail to reportRtcClean, roomId {}, response {}", roomId, response.body());
//            }
//        }catch (IOException e) {
//            log.error("exception when reportRtcClean, roomId {}", roomId, e);
//        }
//    }


    /**
     * 回调状态
     * @param roomId
     */
    public void reportStatus(String roomId, String status, String msg){
        long bizTime = System.currentTimeMillis();
        Constants.callBackExecutor.submit(new Runnable() {
            public void run() {
                CallConfig.BizCallBack bizCallBack = contextService.getTask().getCallConfig().getBizCallBack();
                if(bizCallBack == null) {
                    log.warn("biz call back is not set, roomId {}, status {}", roomId, status);
                    return;
                }

                String serverMessageUrl = bizCallBack.getServerMessageUrl();
                if(StringUtils.isBlank(serverMessageUrl)) {
                    log.warn("server message url is not set, roomId {}", roomId);
                    return;
                }

                log.info("report status {} roomId {}, url {}", status, roomId, serverMessageUrl);
                TaskStatus taskStatus = TaskStatus.builder()
                        .roomId(roomId)
                        .msg(msg)
                        .status(status)
                        .bizTime(bizTime)
                        .build();

                RequestBody body = RequestBody.create(JSON_MEDIA_TYPE, JSON.toJSONString(taskStatus));
                Request request = new Request.Builder()
                        .url(serverMessageUrl)
                        .post(body)
                        .build();

                try (Response response = client.newCall(request).execute()) {
                    if(!response.isSuccessful()) {
                        log.error("fail to reportStatus, roomId {}, response {}", roomId, response.body());
                    }
                }catch (IOException e) {
                    log.error("exception when reportStatus, roomId {}", roomId, e);
                }
            }
        });
    }

    public void hangup(String roomId, String channel, long bizTime) {
        Constants.callBackExecutor.submit(new Runnable() {
            public void run() {
                CallStop callStop = new CallStop();
                callStop.setRoomId(roomId);
                callStop.setChannel(channel);
                callStop.setHangupBy("AI");
                callStop.setBizTime(bizTime);

                log.info("hangup info, roomId {}, content {}", roomId, callStop);
                RequestBody callStopRequestBody = RequestBody.create(JSON_MEDIA_TYPE, JSON.toJSONString(callStop));

                Request request = new Request.Builder()
                        .url(config.getUrl() + "/rtc/stop")
                        .post(callStopRequestBody)
                        .build();

                try (Response response = client.newCall(request).execute()) {
                    if(!response.isSuccessful()) {
                        log.error("fail to hangup, roomId {}, response {}", roomId, response.message());
                    }
                }catch (IOException e) {
                    log.error("exception when hangup, roomId {}", roomId, e);
                }
            }
        });
    }
}
