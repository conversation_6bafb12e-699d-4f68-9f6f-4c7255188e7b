package com.mantis.rtc.service.tts.cosyvoice.stream;

import com.alibaba.dashscope.audio.ttsv2.SpeechSynthesizer;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;

import java.util.concurrent.locks.Lock;

/**
 * 您需要在项目中引入org.apache.commons.pool2和DashScope相关的包。
 * 推荐使用对象池的方式复用SpeechSynthesizer对象，可以进一步降低反复创建、销毁对象带来的内存和时间开销。
 * 请您在运行Java服务前通过环境变量的方式在机器中提前按需配置好对象池的大小。对象池配置参数如下：
 * COSYVOICE_OBJECTPOOL_SIZE，对象池大小，推荐配置为您的峰值并发数的1.5~2倍。
 */

public class CosyvoiceObjectPool {
    public static GenericObjectPool<SpeechSynthesizer> synthesizerPool;
    public static int DEFAULT_OBJECT_POOL_SIZE = 3;
    private static Lock lock = new java.util.concurrent.locks.ReentrantLock();

    public static GenericObjectPool<SpeechSynthesizer> getInstance() {
        lock.lock();
        if (synthesizerPool == null) {
            // 建议设置为服务器最大并发连接数的1.5到2倍。
            int objectPoolSize = DEFAULT_OBJECT_POOL_SIZE;
            SpeechSynthesizerObjectFactory speechSynthesizerObjectFactory = new SpeechSynthesizerObjectFactory();
            GenericObjectPoolConfig<SpeechSynthesizer> config = new GenericObjectPoolConfig<>();
            config.setMaxTotal(objectPoolSize);
            config.setMaxIdle(objectPoolSize);
            config.setMinIdle(objectPoolSize);
            synthesizerPool = new GenericObjectPool<>(speechSynthesizerObjectFactory, config);
        }
        lock.unlock();
        return synthesizerPool;
    }
}