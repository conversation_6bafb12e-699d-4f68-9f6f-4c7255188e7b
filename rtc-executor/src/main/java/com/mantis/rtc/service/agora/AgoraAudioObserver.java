package com.mantis.rtc.service.agora;

import java.nio.ByteBuffer;

import com.mantis.rtc.pojo.Constants;
import com.mantis.rtc.service.CallContextService;
import com.mantis.rtc.service.rtc.utils.RtcConstants;

import io.agora.rtc.AgoraLocalUser;
import io.agora.rtc.AudioFrame;
import io.agora.rtc.IAudioFrameObserver;
import io.agora.rtc.VadProcessResult;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class AgoraAudioObserver  implements IAudioFrameObserver {
    private CallContextService contextService = null;
    private String roomId = null;
    
    public AgoraAudioObserver(CallContextService contextService, String roomId) {
        this.contextService = contextService;
        this.roomId = roomId;
    }

    @Override
    public int onPlaybackAudioFrame(AgoraLocalUser agoraLocalUser, String channelId, AudioFrame frame) {

        if(!roomId.equalsIgnoreCase(channelId)) {
            log.error("onPlaybackAudioFrame error, channelId: {} is not equal to roomId: {}", channelId, roomId);
            return 0;
        } 
        
        ByteBuffer buffer = frame.getBuffer();
        if (buffer == null || !buffer.hasRemaining()) {
            log.error("invalid data buffer, channelId: {}", channelId);
            return 0;
        }

         // 获取音频数据
        int length = buffer.remaining();
        byte[] data = new byte[length];
        buffer.get(data);

        // 录制
        contextService.getRecordService().recordUser(data);

        // 不允许打断， 忽略语音
        if(!contextService.isAllowInterrupted()) {
            log.trace("call is not allow interrupt, skip new message, roomId {}", channelId);
            return 0;
        }

        // 欢迎语正在播放，忽略语音
        if(!contextService.isWelcomeComplete()) {
            log.debug("welcome is not complete, skip new message, roomId {}", channelId);
            return 0;
        }
        // 启动挂机程序后，不在接收语音
        if(contextService.isHookStatus()){
            log.debug("hook skip new message, roomId {}", channelId);
            return 0;
        }

        // 静默期内，不接受新的语音数据
        if(contextService.isSilence()) {
            log.trace("in silence status, skip new message, roomId {}", channelId);
            return 0;
        }

        Float volumeGain = contextService.getTask().getCallConfig().getAgoraRtc().getVolumeGain();
        if(volumeGain != null && volumeGain > 0 && volumeGain < 1) {
            Constants.adjustVolume(data, length, volumeGain);
        }

        contextService.getRtcToVadBuffer().enqueue(data);

        // 执行VAD 检测
        while (contextService.getRtcToVadBuffer().size() >= RtcConstants.VAD_16000_BATCH_SAMPLE_COUNT) {
            // 如果任务结束，退出
            if(!contextService.isWorking()) {
                log.info("rtc2Vad task exit, roomId {}", channelId);
                break;
            }
            // 获取VAD数据
            byte[] dequeue = contextService.getRtcToVadBuffer().dequeue(RtcConstants.VAD_16000_BATCH_SAMPLE_COUNT);
            if(dequeue == null || dequeue.length == 0) {
                continue;
            }
            // 执行vad检测
            contextService.getVadService().apply(dequeue);
        }
        
        return 1;
    }

    @Override
    public int onMixedAudioFrame(AgoraLocalUser agoraLocalUser, String channelId, AudioFrame frame) {
        log.info("onMixedAudioFrame success");
        return 1;
    }   

    @Override
    public int onPlaybackAudioFrameBeforeMixing(AgoraLocalUser agoraLocalUser, String channelId, String userId,
            AudioFrame frame, VadProcessResult vadResult) {
        log.info("onPlaybackAudioFrameBeforeMixing success");
        return 1;
    }

    @Override
    public int onRecordAudioFrame(AgoraLocalUser agoraLocalUser, String channelId, AudioFrame frame) {
        return 1;
    }

    @Override
    public int onEarMonitoringAudioFrame(AgoraLocalUser agoraLocalUser, AudioFrame frame) {
        return 1;
    }

    @Override
    public int getObservedAudioFramePosition() {
        return 1;
    }
}
