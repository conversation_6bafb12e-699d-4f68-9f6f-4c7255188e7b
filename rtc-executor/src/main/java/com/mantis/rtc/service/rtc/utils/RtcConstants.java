package com.mantis.rtc.service.rtc.utils;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class RtcConstants {
    /**
     * 音频目录
     */
    public static final String AUDIO_DIR = "/mantis/rtc_audios";
    /**
     * 音频采样率
     */
    public static final int SAMPLE_RATE_16000 =  16000;

    /**
     * 音频采样率
     */
    public static final int SAMPLE_RATE_8000 =  8000;
    /**
     * 音频声道数
     */
    public static final int RTC_AUDIO_CHANNELS = 1;
    /**
     * VAD需要的采样数
     */
    public static final int VAD_16000_BATCH_SAMPLE_COUNT = 1024;
    public static final int VAD_8000_BATCH_SAMPLE_COUNT = 512;

}
