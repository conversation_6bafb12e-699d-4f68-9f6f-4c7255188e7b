package com.mantis.rtc.service.dog;

import com.mantis.micor.pojo.CallTask;
import com.mantis.rtc.service.CallContextService;
import com.mantis.rtc.service.TaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@Slf4j
public class DogService implements TaskService {

    @Autowired
    private CallContextService contextService;
    private CallTask task;

    private boolean isUsed = false;

    public void markExecutorUsed() {
        this.isUsed = true;
    }

    private boolean isUsed() {
        return this.isUsed;
    }

    @Override
    public boolean init(CallTask task) {
        this.task = task;
        this.isUsed = false;
        // 检测线程启动
        DogMonitorTask dogMonitorTask = new DogMonitorTask();
        new Thread(dogMonitorTask).start();
        return true;
    }

    @Override
    public void clean() {
        this.task = null;
        this.isUsed = false;
    }

    @Override
    public int order() {
        return 0;
    }

    class DogMonitorTask implements Runnable {
        @Override
        public void run() {
            long l = System.currentTimeMillis();
            while (true) {
                if(task == null) {
                    break;
                }

                String roomId = task.getRoomId();
                String channel = task.getCallConfig().getChannel();


                if (!contextService.isWorking()) {
                    log.info("dog monitor quit because task stop, roomId {}", roomId);
                    break;
                }

                if(isUsed()) {
                    log.info("dog monitor quit because executor used, roomId {}", roomId);
                    break;
                }

                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    log.error("dog monitor sleep error", e);
                }

                long l1 = System.currentTimeMillis();
                // 如果超过2分钟闲置，则自动清理
                if (l1 - l > 1000 * 60 * 1) {
                    log.info("dog monitor, executor task is not used timeout, will hangup, roomId {}", roomId);
                    contextService.hangupManual(roomId, channel, System.currentTimeMillis());
                }
            }
        }
    }
}
