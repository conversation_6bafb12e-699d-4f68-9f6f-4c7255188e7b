package com.mantis.rtc.service.llm;

import com.alibaba.fastjson.JSON;
import com.mantis.micor.pojo.CallConfig;
import com.mantis.rtc.pojo.Constants;
import com.mantis.rtc.pojo.chat.BotSubtitleEventDTO;
import com.mantis.rtc.service.CallContextService;
import com.mantis.rtc.utils.RegularExpression;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.io.IOException;

import static com.mantis.rtc.pojo.Constants.JSON_MEDIA_TYPE;

@Slf4j
@Service
public class BotSubtitleService {

    @Autowired
    private CallContextService contextService;

    @Autowired
    @Qualifier("okhttpClient")
    private OkHttpClient client;

    public void sendUserMsg(String words, String roomId, String msgId ) {
        log.info("sendUserMsg, roomId {}, msgId {}, content {}", roomId, msgId, words);
        Constants.callBackExecutor.submit(new Runnable() {
            public void run() {
                try {
                    BotSubtitleEventDTO botSubtitleEventDTO = new BotSubtitleEventDTO();
                    botSubtitleEventDTO.setMsgId(msgId);
                    botSubtitleEventDTO.setRoomId(roomId);
                    botSubtitleEventDTO.setText(words);
                    botSubtitleEventDTO.setUserId("user_" + roomId);
                    httpUploadSubtitle(botSubtitleEventDTO, roomId);
                }
                catch (Exception ex) {
                    log.error("roomId {}, content {}, sendUserMsg fails: {}", roomId, words, ex.getMessage());
                }
            }
        });
    }

    public void sendAIMsg(String words, String roomId, String msgId) {
        if(StringUtils.isBlank(words)) {
            return;
        }

        Constants.callBackExecutor.submit(new Runnable() {

            public void run() {
                log.info("sendAIMsg, roomId {}, msgId {}, content {}", roomId, msgId, words);
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }

                // 机器人识别反馈
                if(RegularExpression.isCheckPass(RegularExpression.ROBOT_CHAT_PATTERN, words)){
                    log.info("found AI response with hook, roomId {}, msgId {}, content {}", roomId, msgId, words);
                    contextService.setHookStatus(true);
                }

                try {
                    BotSubtitleEventDTO botSubtitleEventDTO = new BotSubtitleEventDTO();
                    botSubtitleEventDTO.setMsgId(msgId);
                    botSubtitleEventDTO.setRoomId(roomId);
                    botSubtitleEventDTO.setText(words);
                    botSubtitleEventDTO.setUserId("bot_" + roomId);
                    httpUploadSubtitle(botSubtitleEventDTO, roomId);
                }
                catch (Exception ex) {
                    log.error("roomId {}, content {}, sendAIMsg fails: {}", roomId, words, ex.getMessage());
                }
            }
        });
    }

    private void httpUploadSubtitle(BotSubtitleEventDTO botSubtitleEventDTO, String roomId) {
        CallConfig.SubtitleCallBackConfig config = contextService.getTask().getCallConfig().getSubtitleCallBackConfig();
        if(config == null || StringUtils.isBlank(config.getServerMessageUrl())) {
            log.warn("subtitle call back config is null or empty, roomId {}", roomId);
            return;
        }

        String serviceUrl;
        if(contextService.isRtcChannel(contextService.getTask())) {
            serviceUrl = config.getServerMessageUrl()  + "/wechat/" + roomId;
        } else if(contextService.isAgoraRtcChannel(contextService.getTask())) {
            serviceUrl = config.getServerMessageUrl()  + "/wechat/" + roomId;
        } else {
            serviceUrl = config.getServerMessageUrl()  + "/cc/" + roomId;
        }

        RequestBody body = RequestBody.create(JSON_MEDIA_TYPE, JSON.toJSONString(botSubtitleEventDTO));
        Request request = new Request.Builder()
                .url(serviceUrl)
                .post(body)
                .build();
        try (Response response = client.newCall(request).execute()) {
            if(!response.isSuccessful()) {
                log.error("fail to httpUploadSubtitle, roomId {}, response {}", roomId, response.body() == null ? response.message() : response.body().string());
            }
        }catch (IOException e) {
            log.error("exception when httpUploadSubtitle, roomId {}", roomId, e);
        }
    }
}