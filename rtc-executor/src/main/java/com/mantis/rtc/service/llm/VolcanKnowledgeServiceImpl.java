package com.mantis.rtc.service.llm;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mantis.micor.pojo.CallConfig;
import com.mantis.micor.pojo.CallTask;
import com.mantis.rtc.pojo.chat.VolcSearchKnowledgeRespDTO;
import com.mantis.rtc.service.CallContextService;
import com.volcengine.service.SignableRequest;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;


/**
 * 火山方舟知识库
 *
 * @author: leo
 * @Date: 2022/3/23 14:03
 * @Description:
 */
@Scope("singleton")
@Service("volcanKnowledgeServiceImpl")
@Slf4j
public class VolcanKnowledgeServiceImpl implements KnowledgeService {

    @Autowired
    private CallContextService callContextService;

    @Autowired
    @Qualifier("httpClient")
    private HttpClient httpClient;


    private static final String HOST = "api-knowledgebase.mlp.cn-beijing.volces.com";
    private static final String PATH = "/api/knowledge/collection/search_knowledge";

    @Override
    public Map<String, String> searchKnowledge(CallTask task, String query, String msgId) {
        CallConfig.Knowledge knowledge = task.getCallConfig().getKnowledge();
        if (knowledge == null) {
            return null;
        }

        if (!"volcan".equalsIgnoreCase(knowledge.getVendor())) {
            return null;
        }

        try {
            return searchKnowledgeApi(knowledge.getVolcan().getName(), query, knowledge, task.getRoomId(), msgId);
        } catch (Exception e) {
            log.error("searchKnowledge exception, {}", query, e);
            return null;
        }
    }

    /**
     * 检索知识库
     *
     * @param resourceName 知识库名称
     * @param query        查询条件
     * @return
     */
    private Map<String, String> searchKnowledgeApi(String resourceName, String query, CallConfig.Knowledge knowledge, String roomId, String msgId) {
        long startTime = System.currentTimeMillis();
        Map<String, Object> reqMap = new HashMap<>();
        //知识库名称
        reqMap.put("name", resourceName);
        //查询条件
        if (query.length() > 8000) {
            query = query.substring(0, 8000);
        }
        reqMap.put("query", query);
        reqMap.put("dense_weight", 0.8);
        // 取分数最高的1条
        reqMap.put("limit", 1);

        // 支持标签，查询参数
        CallConfig.KnFilter filter = knowledge.getFilter();
        if (filter != null && filter.getTags() != null && filter.getTags().size() > 0) {
            Map<String, Object> query_param_map = new HashMap<>();
            Map<String, Object> doc_filter = new HashMap<>();
            doc_filter.put("op", "must");
            doc_filter.put("field", "tag");
            doc_filter.put("conds", filter.getTags());
            query_param_map.put("doc_filter", doc_filter);
            reqMap.put("query_param", query_param_map);
        }

        // 获取5条进行重排
        Map<String, Object> post_processing = new HashMap<>();
        post_processing.put("rerank_switch", true);
        post_processing.put("retrieve_count", 5);
        post_processing.put("rerank_model", "m3-v2-rerank");
        reqMap.put("post_processing", post_processing);

        String body = JSON.toJSONString(reqMap);
        try {
            SignableRequest request = VolcSign.prepareRequest(HOST, PATH, "POST", null, body, knowledge.getVolcan().getAk(), knowledge.getVolcan().getSk());
            HttpResponse response = httpClient.execute(request);
            int statusCode = response.getStatusLine().getStatusCode();
            String content = EntityUtils.toString(response.getEntity());
            if (StringUtils.isBlank(content)) {
                log.error("volcan searchKnowledge, 返回空, sessionId {}, msgId {}, query: {}， statusCode {}", roomId, msgId, query, statusCode);
                return null;
            }

            VolcSearchKnowledgeRespDTO volcSearchKnowledgeRespDTO = JSONObject.parseObject(content, VolcSearchKnowledgeRespDTO.class);

            if (null != volcSearchKnowledgeRespDTO && Integer.valueOf(0).equals(volcSearchKnowledgeRespDTO.getCode())) {
                if (null != volcSearchKnowledgeRespDTO.getData() && null != volcSearchKnowledgeRespDTO.getData().getResult_list() && volcSearchKnowledgeRespDTO.getData().getResult_list().size() > 0) {
                    List<VolcSearchKnowledgeRespDTO.VolcSearchKnowledge> resultList = volcSearchKnowledgeRespDTO.getData().getResult_list();
                    // 按 score 降序排序
                    resultList.sort((o1, o2) -> Double.compare(o2.getRerank_score(), o1.getRerank_score()));
                    Map<String, String> resultMap = new HashMap<>();
                    Double score = resultList.get(0).getRerank_score();
                    String returnQuery = resultList.get(0).getOriginal_question();
                    String answer = null;

                    for (VolcSearchKnowledgeRespDTO.TableChunkFields tableChunkFields : resultList.get(0).getTable_chunk_fields()) {
                        if ("问题".equalsIgnoreCase(tableChunkFields.getField_name())) {
                            returnQuery = tableChunkFields.getField_value();
                        }
                        if ("答案".equalsIgnoreCase(tableChunkFields.getField_name())) {
                            answer = tableChunkFields.getField_value();
                        }
                    }

                    if (StringUtils.isBlank(answer)) {
                        log.info("volcan searchKnowledge, query {}, msgId {}, response null", query, msgId);
                        return null;
                    }
                    long endTime = System.currentTimeMillis();
                    callContextService.setMsgInfoTrack(msgId, "knowledge", System.currentTimeMillis() - startTime);
                    log.info("volcan searchKnowledge, roomId {}, msgId {}, query {}, matched_query {}, matched_answer {}, score {} limit {}, taken: {}, perf", roomId, msgId, query, returnQuery, answer, score, knowledge.getVolcan().getScoreLimit(), endTime - startTime);

                    // 如果最大分数小于指定值，返回为空
                    if (score < knowledge.getVolcan().getScoreLimit()) {
                        return null;
                    }

                    resultMap.put(returnQuery, answer);
                    return resultMap;
                } else {
                    log.info("volcan searchKnowledge, no available, roomId {}, msgId {}, query {}", roomId, msgId, query);
                }
            }
        } catch (Exception e) {
            log.error("volcan knowledge, exception, sessionId {}, msgId {}, query: {}", roomId, msgId, query, e);
        }
        return null;
    }
}
