package com.mantis.rtc.service.llm;

import com.mantis.micor.pojo.CallTask;
import com.mantis.rtc.pojo.chat.Message;
import com.mantis.rtc.service.TaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 对话消息缓存服务, 包括：
 * 1. AI消息的流式缓存与获取
 * 2. 对话所有消息的缓存与获取
 *
 * 需要存储的内容：
 * 1. 对话的最后一个访客消息Id
 * 2. 消息列表
 * 3. AI消息分片
 *
 *  需要考虑：
 *  1、存储到内存
 *  2、AI消息的保存：
 *  - AI消息流式返回，保存策略和时机
 *  - 被访客新消息打断的场景,此时AI消息是不全的
 *
 */
@Slf4j
@Service
public class ChatContextServiceMemoryImpl implements TaskService {

    /**
     * 对话历史
     */
    private final List<Message> chatHistory = new ArrayList<>();
    /**
     * 对话最后一条访客消息Id
     */
    private String latestMsgId;

    private final Map<String, String> aiMsgTrunkCache = new HashMap<>();

    /**
     * 保持对话最后一条访客消息Id
     * @param sessionId
     * @param msgId
     */
    public void saveLatestMsgId(String sessionId, String msgId) {
        this.latestMsgId = msgId;
    }

    /**
     * 获取对话最后一条访客消息Id
     * @param sessionId
     * @return
     */
    public String getLatestMsgId(String sessionId) {
        return latestMsgId;
    }

    /**
     * 保存对话的消息
     * @param roomId
     * @param msg
     */
    public void saveMessage(String roomId, Message msg) {
        log.debug("save msg into context, roomId {}, msg {}", roomId, msg);
        chatHistory.add(msg);
    }

    /**
     * 获取对话的所有消息
     * @param sessionId 对话Id
     * @param count 获取的消息数量，null表示获取所有
     * @return
     */
    public List<Message> cloneMessages(String sessionId, Integer count) {
        return new ArrayList<>(chatHistory);
    }

    public List<Message> getChatHistory() {
        return chatHistory;
    }

    /**
     * 追加AI消息分片，
     * 消息是流式接收的，故需要append后写入
     * @param msgId 访客消息Id
     * @param msg 消息片段
     */
    public void onAIMsgTrunk(String msgId, String msg) {
        aiMsgTrunkCache.put(msgId, msg);
    }

    /**
     * 获取AI消息
     * @param msgId, 访客消息Id
     * @return
     */
    public String getAIMsg(String msgId) {
        return aiMsgTrunkCache.get(msgId);
    }

    @Override
    public boolean init(CallTask task) {
        this.chatHistory.clear();
        this.aiMsgTrunkCache.clear();
        this.latestMsgId = null;
        log.info("init chat context complete, roomId {}", task.getRoomId());
        return true;
    }

    @Override
    public void clean() {
        this.chatHistory.clear();
        this.aiMsgTrunkCache.clear();
        this.latestMsgId = null;
    }

    @Override
    public int order() {
        return 99;
    }
}

