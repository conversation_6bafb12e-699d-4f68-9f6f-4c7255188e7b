package com.mantis.rtc.service.tts.cosyvoice.stream;

import com.alibaba.dashscope.audio.ttsv2.SpeechSynthesisAudioFormat;
import com.alibaba.dashscope.audio.ttsv2.SpeechSynthesisParam;
import com.alibaba.dashscope.audio.ttsv2.SpeechSynthesizer;
import com.mantis.micor.pojo.CallConfig;
import com.mantis.micor.pojo.CallTask;
import com.mantis.rtc.pojo.Constants;
import com.mantis.rtc.service.CallContextService;
import com.mantis.rtc.service.TaskService;
import com.mantis.rtc.service.tts.TTSStreamService;
import com.mantis.rtc.service.tts.cache.TTSCacheService;
import com.mantis.rtc.utils.Utils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.WebSocket;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import static com.mantis.rtc.service.rtc.utils.RtcConstants.SAMPLE_RATE_8000;

/**
 * CosyVoiceService 的问题，在于api不能透传msgId参数，因此要实现打断效果会比较麻烦。只能每个消息创建连接，为了缩短耗时，提前初始化连接池
 * 对象池大小，推荐配置为您的峰值并发数的1.5~2倍
 * 配置连接池大小，推荐配置为您的峰值并发数的2倍以上。默认值为32。
 * CosyVoice大模型的服务协议为WebSocket，在高并发场景下，如果不断地创建WebSocket连接会增加连接耗时且会有较大资源消耗。您在通过DashScope Java SDK调用CosyVoice语音大模型服务时，可以使用连接池和对象池这两种资源池，降低高并发场景下的程序开销。
 * DashScope Java SDK使用了OkHttp3提供的连接池复用WebSocket连接，降低不断创建WebSocket连接的耗时和资源开销。
 * 连接池为SDK默认开启的优化项，您需要根据使用场景配置连接池的大小。
 * DASHSCOPE_CONNECTION_POOL_SIZE， 配置连接池大小。
 * DASHSCOPE_MAXIMUM_ASYNC_REQUESTS 配置最大异步请求数。推荐配置为和连接池大小一致。默认值为32。
 * DASHSCOPE_MAXIMUM_ASYNC_REQUESTS_PER_HOST 配置单host最大异步请求数。推荐配置为和连接池大小一致。默认值为32。
 * https://help.aliyun.com/zh/model-studio/developer-reference/high-concurrency-scenarios?spm=a2c4g.11186623.help-menu-2400256.d_2_4_0_3.1f2e4b04UjSiOc
 */
@Service
@Slf4j
public class CosyVoiceStreamService implements TaskService, TTSStreamService {

    @Autowired
    private CallContextService contextService;

    @Autowired
    private TTSCacheService ttsCacheService;

    private CallTask task = null;
    private String roomId = "";

    private final Map<String, SpeechSynthesizer> speechSynthesizerMap = new ConcurrentHashMap<>();

    private CallConfig.TTS tts;


    /**
     * 同步调用tts
     * @param sentence
     * @param roomId
     */
    @Override
    public void call(String sentence, String roomId, String msgId, boolean complete) {
        if(StringUtils.isBlank(sentence)) {
            // 停止静默
            contextService.setSilence(false, "empty query in tts", msgId);
            return;
        }

        // 直接从内存中获取tts数据
        byte[] bytes = ttsCacheService.getCache().get(sentence);

        if(bytes != null) {
            if(StringUtils.isNotBlank(msgId) && msgId.contains("welcome")) {
                log.info("on tts data from cache for welcome {}, roomId {}, msgId {}", sentence, roomId, msgId);
                contextService.getWelcomeBuffer().enqueue(bytes);
                if(complete) {
                    contextService.setWelcomeSent(true);
                }
            } else {
                log.info("on tts data from cache for sentence {}, roomId {}, msgId {}", sentence, roomId, msgId);
                // 存入缓存
                contextService.getTtsToRtcBuffer().enqueue(bytes, msgId);
                // 停止静默
                contextService.setSilence(false, "complete tts by cache", msgId);
            }
            return;
        } else {
            // 如果未能从缓存获取，调用流式接口获取
            log.info("fail to get audio from cache, call tts stream service, send text {} to tts, roomId {}, msgId {}", sentence, roomId, msgId);
            try{
                startSession(roomId, msgId);
                speechSynthesizerMap.get(msgId).streamingCall(sentence);
            }finally {
                completeSession(msgId);
            }
        }
    }

    @Override
    public void onChatStreamStart(String roomId, String msgId){
        startSession(roomId, msgId);
    }

    private void startSession(String roomId, String msgId) {
        SpeechSynthesizer speechSynthesizer = borrowSynthesizer(roomId, msgId);
        if(speechSynthesizer == null) {
            log.error("fail to borrow synthesizer roomId {}, msgId {}", roomId, msgId);
            return;
        }

        log.info("try to start cosyvoice tts session, roomId {}, msgId {}", roomId, msgId);

        speechSynthesizerMap.put(msgId, speechSynthesizer);

        SpeechSynthesisParam param;
        if(task.getSampleRate() == SAMPLE_RATE_8000) {
            param = SpeechSynthesisParam.builder()
                    .apiKey(tts.getCosyvoice().getAppkey())
                    .model("cosyvoice-v2")
                    .voice(tts.getCosyvoice().getVoiceId())
                    .format(SpeechSynthesisAudioFormat.PCM_8000HZ_MONO_16BIT) // 流式合成使用PCM或者MP3
                    .build();
        } else {
            param = SpeechSynthesisParam.builder()
                    .apiKey(tts.getCosyvoice().getAppkey())
                    .model("cosyvoice-v2")
                    .voice(tts.getCosyvoice().getVoiceId())
                    .format(SpeechSynthesisAudioFormat.PCM_16000HZ_MONO_16BIT) // 流式合成使用PCM或者MP3
                    .build();
        }

        speechSynthesizer.updateParamAndCallback(param, new CosyVoiceResultCallback(contextService, roomId, msgId));
    }

    private SpeechSynthesizer borrowSynthesizer(String roomId, String msgId) {
        log.info("try to borrow synthesizer, roomId {}, msgId {}", roomId, msgId);
        int maxmaxRetry = 3;
        while (maxmaxRetry-- > 0) {
            try {
                return CosyvoiceObjectPool.getInstance().borrowObject();
            } catch (Exception e) {
                log.warn("fail to borrow synthesizer roomId {}, msgId {}, maxRetry {}", roomId, msgId, maxmaxRetry, e);
                Utils.sleep(300);
            }
        }
        log.error("fail to borrow synthesizer roomId {}, msgId {}", roomId, msgId);
        return null;
    }


    @Override
    public void completeSession(String msgId) {
        SpeechSynthesizer speechSynthesizer = speechSynthesizerMap.get(msgId);
        if(speechSynthesizer == null) {
            log.error("fail to got synthesizer when completeSession, roomId {}, msgId {}", roomId, msgId);
            return;
        }
        speechSynthesizer.streamingComplete();
        CosyvoiceObjectPool.getInstance().returnObject(speechSynthesizer);
        speechSynthesizerMap.remove(msgId);
        log.info("cosyvoice tts complete, roomId {}, msgId {}, requestId {}, first package delay {}", roomId, msgId, speechSynthesizer.getLastRequestId(), speechSynthesizer.getFirstPackageDelay());

    }

    @Override
    public void clean() {
        log.info("try to clean cosyvoice tts, roomId {}", roomId);
        Set<String> strings = speechSynthesizerMap.keySet();
        for (String msgId: strings) {
            SpeechSynthesizer speechSynthesizer = speechSynthesizerMap.get(msgId);
            if(speechSynthesizer != null) {
                speechSynthesizer.streamingComplete();
                CosyvoiceObjectPool.getInstance().returnObject(speechSynthesizer);
            }

        }
        speechSynthesizerMap.clear();
    }

    public void streamingCall(String sentence, String msgId) {
        if(StringUtils.isBlank(sentence)) {
            return;
        }

        // 打断模式下，不再送文本消息到tts
        if(contextService.isInterrupted(msgId)) {
            log.info("tts streamingCall skip, because interrupted, roomId {}", roomId);
            return;
        }

        if(!contextService.isCurrentMsgId(msgId)) {
            log.info("tts streamingCall skip, because isCurrentMsgId, roomId {}, msgId {}, currentMsgId {}", roomId, msgId, contextService.getCurrentMsgId());
            return;
        }
        SpeechSynthesizer speechSynthesizer = speechSynthesizerMap.get(msgId);
        if(speechSynthesizer == null) {
            log.error("streamingCall, fail to get synthesizer, roomId {}, msgId {}", roomId, msgId);
            return;
        }
        speechSynthesizer.streamingCall(sentence);
    }

    @Override
    public boolean init(CallTask task) {
        this.task = task;
        this.roomId = task.getRoomId();
        this.tts = task.getCallConfig().getTts();

        String vendor = task.getCallConfig().getTts().getVendor();
        if(!vendor.equalsIgnoreCase(Constants.TTS_VENDOR_COSYVOICE)) {
            return true;
        }

        if (StringUtils.isBlank(tts.getCosyvoice().getAppkey())) {
            log.error("cosyvoice, tts appkey is invalid, roomId {}", roomId);
            return false;
        }

        if (StringUtils.isBlank(tts.getCosyvoice().getVoiceId())) {
            log.error("cosyvoice, tts voiceId is invalid, roomId {}", roomId);
            return false;
        }
        log.info("complete cosyvoice tts init, roomId {}", roomId);
        return true;
    }

    @Override
    public int order() {
        return 3;
    }
}
