package com.mantis.rtc.service.agora;


import com.mantis.micor.pojo.CallTask;
import com.mantis.rtc.service.CallContextService;
import com.mantis.rtc.service.TaskService;
import com.mantis.rtc.service.rtc.utils.RtcConstants;
import com.mantis.rtc.utils.Utils;
import io.agora.rtc.*;
import lombok.NonNull;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.ByteBuffer;
import java.util.concurrent.atomic.AtomicBoolean;


@Slf4j
@Service
public class AgoraRtcService implements TaskService {

    @Autowired
    private CallContextService contextService;

    // ------ --- --- --- --- --- ---  实例变量
    private AgoraService agoraService = null;
    private AgoraRtcConn conn = null;
    private AgoraMediaNodeFactory mediaNodeFactory = null;
    private AgoraLocalAudioTrack customAudioTrack;
    private AgoraAudioPcmDataSender audioFrameSender;

    private String roomId = "";
    private Thread pushAudioThread = null;

    /**
     * 提前初始化相关资源，以保证发起呼叫时可以快速响应
     * 需要初始化的资源包括：
     * 0. rtc的初始化
     * 1. vad
     * 2. asr
     * 3. tts
     * 4. 大模型
     */
    public boolean init(CallTask task) {

        if(!contextService.isAgoraRtcChannel(task)) {
            return true;
        }

        if(task.getCallConfig().getAgoraRtc() == null) {
            log.error("callConfig.getAgoraRtc() is null, roomId {}", task.getRoomId());
            return false;
        }

        
        log.info("init agora rtc service begin, roomId {}", task.getRoomId());
        long l = System.currentTimeMillis();
        roomId = task.getRoomId();
        String appId = task.getCallConfig().getAgoraRtc().getAppId();
        String appCertificate = task.getCallConfig().getAgoraRtc().getAppCertificate();

        if(agoraService == null) {
            // 初始化Agora服务
            agoraService = new AgoraService();
                    
            // Initializes the AgoraServiceConfig object
            AgoraServiceConfig config = new AgoraServiceConfig();
            // Enables the audio processing module
            config.setEnableAudioProcessor(1);
            // Disables the audio device module (Normally we do not directly connect audio capture or playback devices to a server)
            config.setEnableAudioDevice(0);
            // Disables video
            config.setEnableVideo(0);
            // Sets声网 App ID
            config.setAppId(appId);
            // Initializes the SDK
            log.debug("init agora rtc service initialize start, roomId {}", task.getRoomId());
            int ret = agoraService.initialize(config);
            log.debug("init agora rtc service initialize end, roomId {}", task.getRoomId());

            if (ret != 0) {
                log.error("init agora rtc service fail ret={}", ret);
                return false;
            }else {
                log.info("init agora rtc service success, roomId {}", task.getRoomId());
            }
        }
        

        RtcConnConfig connConfig = new RtcConnConfig();
        connConfig.setAutoSubscribeAudio(1);
        connConfig.setAutoSubscribeVideo(0);
        // 主播
        connConfig.setChannelProfile(Constants.CHANNEL_PROFILE_LIVE_BROADCASTING);
        connConfig.setClientRoleType(Constants.CLIENT_ROLE_BROADCASTER);
        // log.info("init agora rtc service agoraRtcConnCreate start, roomId {}", task.getRoomId());
        conn = agoraService.agoraRtcConnCreate(connConfig);
        // log.info("init agora rtc service agoraRtcConnCreate end, roomId {}", task.getRoomId());

        if (conn == null) {
            log.error("AgoraService.agoraRtcConnCreate fail");
            return false;
        }

        // 根据targetUserId生成全数字的userid
        int botId = generateRobotUserId();

        String token = "";

        // 如果appId是测试环境，则不使用token
        if(!appId.equals("aab8b8f5a8cd4469a63042fcfafe7063")) {
            token = calcToken(appId, appCertificate, roomId, botId);
        }

        // 注册observer
        conn.registerObserver(new AgoraDefaultRtcConnObserver(contextService));

        // 连接
        // log.info("init agora rtc service connect start, roomId {}", task.getRoomId());

        int result = conn.connect(token, roomId, botId+"");

        if (result != 0) {
            log.error("AgoraService.agoraRtcConnCreate fail, result={}", result);
            return false;
        } else {
            log.info("agora rtc service connect success, roomId {}", task.getRoomId());
        }

        Float volumeGain = contextService.getTask().getCallConfig().getAgoraRtc().getVolumeGain();
        conn.getLocalUser().adjustPlaybackSignalVolume((int)(volumeGain * 100));
        int observerRet = conn.getLocalUser().setPlaybackAudioFrameParameters(RtcConstants.RTC_AUDIO_CHANNELS, task.getSampleRate(), 0, 1024);
        if(observerRet != 0) {
            log.error("AgoraService.setPlaybackAudioFrameParameters fail, result={}", observerRet);
            return false;
        }

        // 设置音频observer
        AgoraAudioObserver audioFrameObserver = new AgoraAudioObserver(contextService, roomId);
        int registerRet = conn.getLocalUser().registerAudioFrameObserver(audioFrameObserver);
        if(registerRet != 0) {
            log.error("AgoraService.registerAudioFrameObserver fail, result={}", registerRet);
            return false;
        }

        // 推流线程启动
        PushAudioStreamTask pushAudio = new PushAudioStreamTask();
        // log.info("init agora rtc service pushAudioStreamTask start, roomId {}", task.getRoomId());
        pushAudioThread = new Thread(pushAudio);
        pushAudioThread.start();
        // log.info("init agora rtc service pushAudioStreamTask end, roomId {}", task.getRoomId());

        log.info("init agora rtc service complete, roomId {}, taken {} ms", task.getRoomId(), (System.currentTimeMillis() - l));
        return true;
    }

    @Override
    public void clean() {
        if(!contextService.isAgoraRtcChannel(contextService.getTask())) {
            return;
        }
        pushAudioThread.interrupt();

        long l = System.currentTimeMillis();
        try {
            leaveRoom();
        } catch (Exception e) {
            log.error("leave room exception, roomId {}", roomId, e);
        } finally {
            log.info("clean agora rtc service complete, roomId {}, taken {} ms", roomId, (System.currentTimeMillis() - l));
        }

    }

    @Override
    public int order() {
        return 0;
    }

    /**
     * 打断对话，清理未发送的数据
     */
    public void interrupt() {
        customAudioTrack.clearSenderBuffer();
    }

    private void leaveRoom() {
        if(conn != null) {
            log.info("leaveRoom conn start, roomId {}", roomId);
    
            if (null != audioFrameSender) {
                audioFrameSender.destroy();
                audioFrameSender = null;
            }
            if (null != customAudioTrack) {
                customAudioTrack.clearSenderBuffer();
                conn.getLocalUser().unpublishAudio(customAudioTrack);
                customAudioTrack.destroy();
                customAudioTrack = null;
            }
            int ret = conn.disconnect();
            if (ret != 0) {
                log.error("AgoraService.disconnect fail, result={}", ret);
            }
            // Unregister connection observer
            conn.unregisterObserver();
            conn.getLocalUser().unregisterObserver();
            conn.destroy();
            conn = null;
            log.info("leaveRoom conn end, roomId {}", roomId);
        }
    }

    /**
     * 声网有效期24小时
     */
    private static final int expirationInSeconds = 86400;
    /*
     * 计算token
     * @param appId 声网appId
     * @param appCertificate 声网证书
     * @param roomId 房间号
     * @param userId 用户id
     * @return
     */
    private String calcToken(@NonNull String appId, @NonNull String appCertificate, @NonNull String roomId, @NonNull Integer userId) {
        AgoraRtcTokenBuilder rtcTokenBuilder = new AgoraRtcTokenBuilder();
        String token = rtcTokenBuilder.buildTokenWithUid(appId, appCertificate, roomId, userId, AgoraRtcTokenBuilder.Role.ROLE_PUBLISHER,
                expirationInSeconds, expirationInSeconds);
        return token;
    }


    /**
     * 计算机器人userId
     * @return 全数字的用户ID
     */
    private int generateRobotUserId() {
        return 900000 + (int)(Math.random() * 80000) +  (int)(Math.random() * 10000) +  (int)(Math.random() * 8000);
    }



    /**
     * 推流到RTC线程，10ms打包
     */
    class PushAudioStreamTask implements Runnable {
        int PACKAGE_TIME = 10;
        final int sampleRate = RtcConstants.SAMPLE_RATE_16000;
        final int channels = RtcConstants.RTC_AUDIO_CHANNELS;
        private boolean speakFlag = false;

        public PushAudioStreamTask(){
            if(mediaNodeFactory == null) {
                // 准备发送媒体流，推流到Agora
                mediaNodeFactory = agoraService.createMediaNodeFactory();
            }
            audioFrameSender = mediaNodeFactory.createAudioPcmDataSender();
            // Create audio track
            customAudioTrack = agoraService.createCustomAudioTrackPcm(audioFrameSender);
            // up to 16min,100000 frames, 10000 frames is enough
            customAudioTrack.setMaxBufferedAudioFrameNumber(100000);
            customAudioTrack.setEnabled(1);
            conn.getLocalUser().publishAudio(customAudioTrack);
        }

        @Override
        public void run() {

            // 1ms 的数据大小
            int onePackageSize = sampleRate / 1000 * channels * 2;

            while (!Thread.currentThread().isInterrupted()) {
                if(!contextService.isWorking()){
                    log.info("rtc PushAudioStreamTask quit, roomId {}", roomId);
                    break;
                }

                // 当有新数据写入tts buffer时，标识开始循环
                if(!contextService.getHasRtcDataToPush().get()) {
                    Utils.sleep(20);
                    continue;
                }

                int welcomeSize = contextService.getWelcomeBuffer().size();
                // 1ms的数据取整
                int welcomeSizeHandled = welcomeSize / onePackageSize * onePackageSize;

                // 优先处理欢迎语数据
                byte[] dequeue = contextService.getWelcomeBuffer().dequeue(welcomeSizeHandled);
                if (dequeue == null || dequeue.length == 0) {
                    // 判定欢迎语发送完毕
                    if (contextService.isWelcomeSent() && !contextService.isWelcomeComplete()) {
                        contextService.setWelcomeComplete(true);
                    }

                    // 从正常buffer获取数据
                    int size = contextService.getTtsToRtcBuffer().size();
                    int sizeHandled = size / onePackageSize * onePackageSize;
                    dequeue = contextService.getTtsToRtcBuffer().dequeue(sizeHandled, true);
                }

                // 没有待发送的数据
                if (dequeue == null || dequeue.length == 0) {
                    // 标识tts buffer数据为空
                    contextService.getHasRtcDataToPush().set(false);

                    // 如果曾经说过
                    if(this.speakFlag) {
                        if(contextService.isHookStatus()) {
                            log.info("audio sent complete in hook status, call tel hangup, roomId {}", contextService.getTask().getRoomId());
                            Utils.sleep(2000);
                            // 挂机，清理
                            contextService.hangupManual(roomId, "rtc", System.currentTimeMillis());
                            break;
                        }else {
                            // 标识AI发言完毕
                            log.info("ai speech complete, roomId {}", roomId);
                            contextService.setAiSpeakEndTime(System.currentTimeMillis());
                        }
                    }
                    this.speakFlag = false;
                    Utils.sleep(100);
                    continue;
                }

                // 有待发送数据
                log.debug("push agora segment begin, dequeue, roomId {}, length {}", roomId, dequeue.length);
                this.speakFlag = true;
                AudioFrame audioFrame = new AudioFrame();
                audioFrame.setBuffer(ByteBuffer.wrap(dequeue));
                audioFrame.setSamplesPerChannel(dequeue.length/2);
                audioFrame.setBytesPerSample(2);
                audioFrame.setChannels(channels);
                audioFrame.setSamplesPerSec(sampleRate);

                try {
                    int i = audioFrameSender.sendAudioPcmData(audioFrame);
                    // 录制方便排查问题
                    contextService.getRecordService().recordAI(dequeue);

                    if (i != 0) {
                        log.error("push audio frame fail, code {}, dequeue size {}, frameLength {}, sample rate {}, channel {} ", i, dequeue.length, dequeue.length/2, sampleRate, channels);
                    }
                } catch (IllegalStateException e) {
                    log.error("pushExternalAudioFrame failed, roomId {}", contextService.getTask().getRoomId(), e);
                }

                // 计算sleep time
                int bytesPerMs = channels * (sampleRate / 1000) * 2;
                long pcmDataTimeMs = dequeue.length / bytesPerMs;
                Utils.sleep(pcmDataTimeMs);
                log.debug("push agora segment end, roomId {}, length {} ms", roomId, pcmDataTimeMs);
            }
        }
    }
}
