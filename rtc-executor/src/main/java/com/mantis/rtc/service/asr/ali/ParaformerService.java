package com.mantis.rtc.service.asr.ali;

//import com.alibaba.dashscope.audio.asr.recognition.Recognition;
//import com.alibaba.dashscope.audio.asr.recognition.RecognitionParam;
//import com.alibaba.dashscope.audio.asr.recognition.RecognitionResult;
//import com.alibaba.dashscope.audio.asr.recognition.timestamp.Sentence;
//import com.alibaba.dashscope.common.ResultCallback;
//import com.alibaba.dashscope.common.Status;
//import com.mantis.micor.pojo.CallConfig;
//import com.mantis.micor.pojo.CallTask;
//import com.mantis.rtc.buffer.VadToAsrBuffer;
//import com.mantis.rtc.config.AsrConfig;
//import com.mantis.rtc.pojo.Constants;
//import com.mantis.rtc.service.CallContextService;
//import com.mantis.rtc.service.TaskService;
//import com.mantis.rtc.service.asr.AsrService;
//import com.mantis.rtc.service.asr.volcan.WebsocketInfo;
//import lombok.extern.slf4j.Slf4j;
//import okhttp3.WebSocket;
//import org.apache.commons.lang3.StringUtils;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.stereotype.Service;
//
//import java.nio.ByteBuffer;
//import java.util.*;
//import java.util.concurrent.ArrayBlockingQueue;
//import java.util.concurrent.ConcurrentHashMap;

/**
 */
//@Slf4j
//@Service
public class ParaformerService {
//      implements   TaskService, AsrService {
//    @Autowired
//    private AsrConfig config;
//
//    @Autowired
//    private CallContextService contextService;
//
//    private static final int MAX_POOL_SIZE = 3;
//
//    /**
//     * Recognition 连接池，用于缓存连接
//     */
//    private final Queue<Recognition> queue = new ArrayBlockingQueue<>(10) ;
//
//    /**
//     * websocket 对应的上下文： seq， msgId, init 状态
//     */
//    private final Map<Recognition, WebsocketInfo> websocketInfoMap = new ConcurrentHashMap<>();
//
//    /**
//     * 消息Id集合，按照顺序记录
//     */
//    private final List<String> msgIds = new ArrayList<>();
//
//    /**
//     * msgId与对应使用的Recognition映射
//     */
//    private final Map<String, Recognition> msgIdWebsocketMap = new ConcurrentHashMap<>();
//
//
//    /**
//     * asr结果， msgId, Asr文本
//     */
//    private final Map<String, String> msgIdAsrResult = new ConcurrentHashMap<>();
//
//    /**
//     * msgId, buffer
//     */
//    private final Map<String, VadToAsrBuffer> buffers = new HashMap<>();
//
//
//    // ------------------------ 变量定义
//    private String roomId = "";
//
//    private CallTask task = null;
//
//    @Override
//    public boolean init(CallTask task) {
//        this.msgIdWebsocketMap.clear();
//        this.websocketInfoMap.clear();
//        this.msgIdAsrResult.clear();
//        this.buffers.clear();
//        this.msgIds.clear();
//        this.queue.clear();
//
//        this.task = task;
//        this.roomId = task.getRoomId();
//
//        CallConfig.ASR asr = task.getCallConfig().getAsr();
//        if (!asr.getVendor().equals("paraformer")) {
//            log.info("no paraformer asr conf, skip init");
//            return true;
//        }
//
//        CallConfig.ParaformerAsr paraformerAsr = asr.getParaformer();
//        if (paraformerAsr == null || StringUtils.isBlank(paraformerAsr.getModel()) || StringUtils.isBlank(paraformerAsr.getApiKey())) {
//            log.error("invalid paraformer asr config, conf {}", asr);
//            return false;
//        }
//
//        initWebsocketPool(task);
//
//        Thread pushThread = new Thread(new ParaformerPushToAsrTask());
//        pushThread.start();
//
//        log.info("paraformer asr init complete, roomId {}", task.getRoomId());
//        return true;
//    }
//
//    @Override
//    public void clean() {
//        while (!this.queue.isEmpty()) {
//            Recognition client = queue.poll();
//            try {
//                client.stop();
//            } catch (Exception e) {
//                log.error("fail to clean paraformer object", e);
//            }
//        }
//
//        this.msgIdWebsocketMap.clear();
//        this.websocketInfoMap.clear();
//        this.msgIdAsrResult.clear();
//        this.buffers.clear();
//        this.msgIds.clear();
//
//        log.info("paraformer asr clean complete, roomId {}", roomId);
//    }
//
//
//    @Override
//    public boolean startSession(CallTask task, String msgId) {
//        // 关联消息Id，分配Recognition
//        Recognition recognition = fetchWebsocketFromQueue(task, msgId);
//        if(recognition == null) {
//            log.error("fail to start session, asr recognition is null, roomId {}, msgId {}", task.getRoomId(), msgId);
//            return false;
//        }
//        return true;
//    }
//
//
//    /**
//     * 当推流数据到缓存时，设置缓存为ready状态
//     * paraformer 没有初始化动作，应该只能以开始推数据作为基准
//     * @param msgId
//     * @param data
//     */
//    @Override
//    public void push(String msgId, byte[] data) {
//        Recognition recognition = msgIdWebsocketMap.get(msgId);
//        if (recognition != null) {
//            WebsocketInfo websocketInfo = websocketInfoMap.get(recognition);
//            if (websocketInfo != null) {
//                websocketInfo.setReady(true);
//            }
//        }
//
//        if (buffers.get(msgId) == null) {
//            buffers.put(msgId, new VadToAsrBuffer());
//        }
//        buffers.get(msgId).enqueue(data);
//    }
//
//    @Override
//    public void doAsr(CallTask task, String msgId, ByteBuffer buffer, boolean isLast) {
//        if(isLast) {
//            return;
//        }
//
//        Recognition recognition = fetchWebsocketFromQueue(task, msgId);
//        if(recognition == null){
//            log.error("doAsr, asr recognition is null, roomId {}, msgId {}, skip", roomId, msgId);
//            return;
//        }
//        log.debug("doAsr, roomId {}, msgId {}, recognition {}", roomId, msgId, recognition);
//        recognition.sendAudioFrame(buffer);
//    }
//
//
//    @Override
//    public String getResult(String msgId) {
//        return msgIdAsrResult.get(msgId);
//    }
//
//    private void setResult(String msgId, String result) {
//        msgIdAsrResult.put(msgId, result);
//    }
//
//    @Override
//    public int order() {
//        return 2;
//    }
//
//    private Recognition fetchWebsocketFromQueue(CallTask task, String msgId) {
//        if(msgIdWebsocketMap.containsKey(msgId)) {
//            return msgIdWebsocketMap.get(msgId);
//        }
//
//        // 每次调用时，都尝试维持连接池的数量
//        initWebsocketPool(task);
//
//        // 分配
//        for (int i = 0; i < 4; i++) {
//            Recognition recognition = queue.poll();
//            if (recognition != null) {
//                onWebsocketAlloc(recognition, msgId);
//                log.info("fetch asr recognition success, left {}, roomId {}", queue.size(), task.getRoomId());
//                return recognition;
//            }
//
//            log.info("fail to get asr recognition, will retry {}, roomId {}", i + 1, task.getRoomId());
//            try {
//                Thread.sleep(200);
//            } catch (InterruptedException e) {
//                log.error("fail to sleep", e);
//            }
//        }
//        log.error("fail to get asr recognition and retry, roomId {}", task.getRoomId());
//        return null;
//    }
//
//    private boolean isMsgAsrReady(String msgId) {
//        Recognition recognition = msgIdWebsocketMap.get(msgId);
//        if (recognition == null) {
//            return false;
//        }
//        WebsocketInfo websocketInfo = websocketInfoMap.get(recognition);
//        return websocketInfo.isReady();
//    }
//
//    /**
//     * 连接完成的处理
//     * @param recognition
//     */
//    private void onWebsocketConnected(Recognition recognition) {
//        log.info("paraformer asr, onWebsocketConnected, roomId {}, obj {}", roomId, recognition);
//        // 加入队列
//        queue.add(recognition);
//        websocketInfoMap.put(recognition, WebsocketInfo.builder().seq(1).isReady(false).build());
//    }
//
//    /**
//     * 关联msgId
//     * @param recognition
//     * @param msgId
//     */
//    private void onWebsocketAlloc(Recognition recognition, String msgId) {
//        msgIdWebsocketMap.put(msgId, recognition);
//        msgIds.add(msgId);
//        WebsocketInfo websocketInfo = websocketInfoMap.get(recognition);
//        log.info("paraformer asr, onWebsocketAlloc, roomId {}, msgId {}, obj {}", roomId, msgId, recognition);
//        websocketInfo.setMsgId(msgId);
//    }
//
//    private void initWebsocketPool(CallTask task) {
//        log.info("paraformer asr, initWebsocketPool, roomId {}, queue size {}", roomId, queue.size());
//        for (int i = 0; i < MAX_POOL_SIZE - queue.size() ; i++) {
//            Constants.ASR_TTS_EXECUTOR.submit(new Runnable() {
//                @Override
//                public void run() {
//                    connectAsr(task);
//                }
//            });
//        }
//    }
//
//    private void connectAsr(CallTask task) {
//        Recognition recognition = new Recognition();
//        RecognitionParam param = RecognitionParam.builder()
//                .model(task.getCallConfig().getAsr().getParaformer().getModel())
//                .format("pcm")
//                .sampleRate(task.getSampleRate())
//                .parameter("semantic_punctuation_enabled",false)
//                // 最大静音时长，单位为毫秒，默认值为800ms, 因为vad有1000毫秒的等待时间，这里设置略小一些，希望能提取获取结果，但是可能会影响识别效果
//                .parameter("max_sentence_silence", 900)
//                .parameter("language_hints", new String[]{"zh"})
//                .apiKey(task.getCallConfig().getAsr().getParaformer().getApiKey())
//                .build();
//        recognition.call(param, new RecognitionCallback(recognition));
//        onWebsocketConnected(recognition);
//    }
//
//
//    class RecognitionCallback extends ResultCallback<RecognitionResult> {
//        private final Recognition recognition;
//
//        public RecognitionCallback(Recognition recognition) {
//            this.recognition = recognition;
//        }
//
//        @Override
//        public void onOpen(Status status) {
//            //sdk的问题，这个回调不会被触发
//            log.info("paraformer asr connection open, roomId {}, requestId {}", roomId, status.getRequestId());
//        }
//
//        @Override
//        public void onEvent(RecognitionResult message) {
//            // 流式处理识别结果
//            Sentence sentence = message.getSentence();
//            if(sentence == null) {
//                return;
//            }
//
//            WebsocketInfo websocketInfo = websocketInfoMap.get(recognition);
//            if (websocketInfo == null) {
//                log.error("onEvent, fail to get websocketInfo by recognition object, roomId {}", task.getRoomId());
//                return;
//            }
//
//            String msgId = websocketInfo.getMsgId();
//            if(StringUtils.isBlank(msgId)) {
//                log.error("onEvent, fail to get msgId by recognition object, roomId {}", task.getRoomId());
//                return;
//            }
//
//            String text = sentence.getText();
//            log.info("onEvent, asr stream result {}, roomId {}, msgId {}, isSentenceEnd {}", text, roomId, msgId, message.isSentenceEnd());
//            if(StringUtils.isNotBlank(text)) {
//                setResult(msgId, text);
//            }
//        }
//
//        @Override
//        public void onComplete() {
//            WebsocketInfo websocketInfo = websocketInfoMap.get(recognition);
//            if(websocketInfo != null) {
//                log.info("asr onComplete, roomId {}, msgId {}, recognition {}", roomId, websocketInfo.getMsgId(), recognition);
//                msgIdWebsocketMap.remove(websocketInfo.getMsgId());
//            }
//            websocketInfoMap.remove(recognition);
//            queue.remove(recognition); // 显式移除失效连接
//            recognition.stop();
//        }
//
//        @Override
//        public void onError(Exception e) {
//            log.error("asr connection error, roomId {}, error: {}, recognition {},", roomId, e.getMessage(), recognition);
//            WebsocketInfo websocketInfo = websocketInfoMap.get(recognition);
//            if(websocketInfo != null) {
//                msgIdWebsocketMap.remove(websocketInfo.getMsgId());
//            }
//            websocketInfoMap.remove(recognition);
//            queue.remove(recognition); // 显式移除失效连接
//            recognition.stop();
//        }
//    }
//
//    class ParaformerPushToAsrTask implements Runnable {
//        @Override
//        public void run() {
//            // 200ms 一个批次，故批次大小为3200, 推送音频，Bytes不应过短或过长，建议在1KB-16KB之间
//            int batch_size = 2 * task.getSampleRate() * 200/1000;
//
//            while (true) {
//                if(!contextService.isWorking()){
//                    log.info("ParaformerPushToAsrTask quit, roomId {}", task.getRoomId());
//                    break;
//                }
//
//                // 100ms 循环一次
//                try {
//                    Thread.sleep(100);
//                } catch (InterruptedException e) {
//                    log.error("fail to sleep");
//                }
//
//                if(msgIds.size() == 0) {
//                    continue;
//                }
//
//                String msgId = msgIds.get(0);
//                boolean msgAsrReady = isMsgAsrReady(msgId);
//                if(!msgAsrReady) {
//                    log.info("msgId {} asr service is not ready, skip , roomId {}", msgId, task.getRoomId());
//                    continue;
//                }
//
//                log.info("begin to asr, msgId {}, roomId {}, batch_size {}", msgId, task.getRoomId(), batch_size);
//                VadToAsrBuffer vadToAsrBuffer = buffers.get(msgId);
//                if(vadToAsrBuffer == null) {
//                    continue;
//                }
//
//                while (true) {
//                    if(!contextService.isWorking()){
//                        log.info("ParaformerPushToAsrTask quit, roomId {}, msgId {}", task.getRoomId(), msgId);
//                        break;
//                    }
//
//                    // 20ms 循环一次
//                    try {
//                        Thread.sleep(200);
//                    } catch (InterruptedException e) {
//                        log.error("fail to sleep");
//                    }
//
//                    byte[] dequeue = vadToAsrBuffer.dequeue(batch_size, true);
//                    if (dequeue == null || dequeue.length == 0) {
//                        // 结束asr会话
//                        doAsr(task, msgId, ByteBuffer.allocate(10), true);
//                        msgIds.remove(msgId);
//                        buffers.remove(msgId);
//                        log.info("ParaformerPushToAsrTask, no data available, roomId {}, msgId {}", task.getRoomId(), msgId);
//                        break;
//                    }
//
//                    doAsr(task, msgId, ByteBuffer.wrap(dequeue), false);
//                    log.debug("ParaformerPushToAsrTask, roomId {}, msgId {}, length {}", task.getRoomId(), msgId, dequeue.length);
//                }
//            }
//        }
//    }
}
