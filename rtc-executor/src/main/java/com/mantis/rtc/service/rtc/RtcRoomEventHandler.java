package com.mantis.rtc.service.rtc;

import com.mantis.rtc.service.CallContextService;
import com.ss.bytertc.engine.UserInfo;
import com.ss.bytertc.engine.handler.IRTCRoomEventHandler;
import com.ss.bytertc.engine.type.LocalStreamStats;
import com.ss.bytertc.engine.type.NetworkQualityStats;
import com.ss.bytertc.engine.type.RTCRoomStats;
import lombok.extern.slf4j.Slf4j;

import java.nio.ByteBuffer;
import java.nio.CharBuffer;
import java.nio.charset.CharacterCodingException;
import java.nio.charset.CharsetDecoder;
import java.nio.charset.StandardCharsets;

@Slf4j
public class RtcRoomEventHandler implements IRTCRoomEventHandler {
    private CallContextService contextService = null;

    public RtcRoomEventHandler(CallContextService contextService){
        this.contextService = contextService;
    }

    @Override
    public void onRoomStateChanged(String roomId, String uid, int state, String extraInfo) {
        if(!roomId.equalsIgnoreCase(contextService.getTask().getRoomId())) {
            return;
        }

        log.info("onRoomStateChanged: roomId {}, uid {}, state {}, extraInfo {}", roomId, uid, state, extraInfo);

        // ErrorCodeRoomDismiss	-1011	服务端调用 OpenAPI 解散房间，所有用户被移出房间。通过 onRoomStateChanged 回调。
        // kErrorCodeJoinRoomRoomForbidden	-1025	房间被封禁。通过 onRoomStateChanged 回调。
        if(state == -1025 || state == -1011) {
            log.info("onRoomStateChanged: roomId {}, state {}, will clean", state, roomId);
            contextService.hangupManual(roomId, "rtc", System.currentTimeMillis());
        }
    }

    @Override
    public void onStreamStateChanged(String roomId, String uid, int state, String extraInfo) {
    }

    @Override
    public void onLeaveRoom(RTCRoomStats stats) {

    }

    @Override
    public void onTokenWillExpire() {
    }

    @Override
    public void onRoomStats(RTCRoomStats stats) {
    }

    @Override
    public void onLocalStreamStats(LocalStreamStats stats) {
    }

    @Override
    public void onUserJoined(UserInfo userInfo, int elapsed) {
        String uid = userInfo.getUid();
        log.info("onUserJoined: uid {}, roomId {}, will send welcome", userInfo.getUid(), contextService.getTask().getRoomId());
        String targetUserId = contextService.getTask().getCallConfig().getRtc().getTargetUserId();
        // 如果用户退房，则清理实例
        if(!uid.equalsIgnoreCase(targetUserId)) {
            log.warn("onUserLeave: uid {}, roomId {} targetUser {}, ignore", uid, contextService.getTask().getRoomId(), targetUserId);
            return;
        }
        // 发送欢迎语
        contextService.sendWelcome();
        contextService.getInfoReportService().reportStatus(contextService.getTask().getRoomId(), "answered", "用户通话接通");
    }

    @Override
    public void onUserLeave(String uid, int reason) {
        log.info("onUserLeave: uid {}, reason {}, roomId {}", uid, reason, contextService.getTask().getRoomId());
        String targetUserId = contextService.getTask().getCallConfig().getRtc().getTargetUserId();
        // 如果用户退房，则清理实例
        if(!uid.equalsIgnoreCase(targetUserId)) {
            log.warn("onUserLeave: uid {}, reason {}, roomId {} targetUser {}, ignore", uid, reason, contextService.getTask().getRoomId(), targetUserId);
            return;
        }

        if(uid.startsWith("talk123456") || uid.startsWith("talkhaichen123") || uid.startsWith("user_")) {
            contextService.hangupManual(contextService.getTask().getRoomId(), "rtc", System.currentTimeMillis());
        }
    }

    @Override
    public void onUserMessageReceived(String uid, String message) {
    }

    @Override
    public void onUserBinaryMessageReceived(String uid, ByteBuffer message) {
    }

    @Override
    public void onUserMessageSendResult(long msgId, int error) {
    }

    @Override
    public void onNetworkQuality(NetworkQualityStats localQuality, NetworkQualityStats[] remoteQualities) {

    }

    private static String byteBufferToString(ByteBuffer buffer) {
        try {
            CharsetDecoder decoder = StandardCharsets.UTF_8.newDecoder();
            CharBuffer charBuffer = decoder.decode(buffer);
            return charBuffer.toString();
        } catch (CharacterCodingException e) {
            return buffer.toString();
        }
    }
}
