package com.mantis.rtc.service;

import com.alibaba.fastjson.JSONObject;
import com.mantis.micor.pojo.CallTask;
import com.mantis.micor.pojo.ExecutorInfo;
import com.mantis.rtc.buffer.RtcToVadBuffer;
import com.mantis.rtc.buffer.TtsToRtcBuffer;
import com.mantis.rtc.buffer.WelcomeBuffer;
import com.mantis.rtc.config.HostMappingConfig;
import com.mantis.rtc.config.LLMConfig;
import com.mantis.rtc.config.VadConfig;
import com.mantis.rtc.pojo.Constants;
import com.mantis.rtc.pojo.chat.Message;
import com.mantis.rtc.pojo.chat.MsgInfoTrack;
import com.mantis.rtc.service.agora.AgoraRtcService;
import com.mantis.rtc.service.asr.AsrService;
import com.mantis.rtc.service.asr.tencent.TencentAsrService;
import com.mantis.rtc.service.asr.volcan.VolcanAsrService;
import com.mantis.rtc.service.dog.DogService;
import com.mantis.rtc.service.gateway.InfoReportService;
import com.mantis.rtc.service.llm.BotSubtitleService;
import com.mantis.rtc.service.llm.ChatContextServiceMemoryImpl;
import com.mantis.rtc.service.llm.LLMService;
import com.mantis.rtc.service.record.RecordService;
import com.mantis.rtc.service.rtc.RtcService;
import com.mantis.rtc.service.slierovad.SlieroVadService;
import com.mantis.rtc.service.tel.TelWebSocketServer;
import com.mantis.rtc.service.tts.TTSStreamService;
import com.mantis.rtc.service.tts.cosyvoice.stream.CosyVoiceStreamService;
import com.mantis.rtc.service.tts.minimax.stream.MinimaxTTSStreamService;
import com.mantis.rtc.service.tts.volcan.stream.VolcanTTSStreamService;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.*;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;

import static com.mantis.rtc.service.rtc.utils.RtcConstants.SAMPLE_RATE_16000;
import static com.mantis.rtc.service.rtc.utils.RtcConstants.SAMPLE_RATE_8000;

@Slf4j
@Data
@Service
public class CallContextService implements EnvironmentAware {
    @Autowired
    private RtcService rtcService;

    @Autowired
    private InfoReportService infoReportService;

    @Autowired
    private SlieroVadService vadService;

    @Autowired
    private VolcanAsrService volcanAsrService;

    @Autowired
    private TencentAsrService tencentAsrService;

    @Autowired
    private LLMService llmService;

    @Autowired
    private AgoraRtcService agoraRtcService;

    @Autowired
    private VolcanTTSStreamService volcanTTSStreamService;

    @Autowired
    private CosyVoiceStreamService cosyVoiceStreamService;

    @Autowired
    private MinimaxTTSStreamService minimaxTTSStreamService;

    @Autowired
    private RecordService recordService;

    @Autowired
    private ChatContextServiceMemoryImpl chatContextService;

    /**
     * 任务服务列表，自动注入所有TaskService的实现类
     */
    @Autowired
    private Set<TaskService> taskServices = new HashSet<>();
    /**
     * 缓冲区服务列表，自动注入所有BufferService的实现类
     */
    @Autowired
    private Set<BufferService> bufferServices = new HashSet<>();

    @Autowired
    private RtcToVadBuffer rtcToVadBuffer;

    @Autowired
    private WelcomeBuffer welcomeBuffer;

    @Autowired
    private TtsToRtcBuffer ttsToRtcBuffer;

    @Autowired
    private LLMConfig llmConfig;

    @Autowired
    private VadConfig vadConfig;

    @Autowired
    private HostMappingConfig hostMappingConfig;

    @Autowired
    private BotSubtitleService botSubtitleService;

    @Autowired
    private DogService dogService;

    static Environment environment;
    /**
     * 是否正常工作
     */
    private boolean isWorking = false;

    /**
     * 当前任务
     */
    private CallTask task = null;
    /**
     * 当前的消息Id
     */
    private String currentMsgId="";

    /**
     * 是否允许打断，默认为允许
     */
    private boolean allowInterrupted = true;

    /**
     * 欢迎语是否发送完成
     */
    private boolean welcomeComplete = false;
    /**
     * 欢迎语是否发送过
     */
    private boolean welcomeSent = false;

    /**
     * 挂机状态 true 等待挂机
     */
    private boolean hookStatus = false;

    /**
     * websocket是否创建成功
     */
    private boolean isFsWebsocketReady;

    private TelWebSocketServer telWebSocketServer;

    private AsrService asrService;

    private TTSStreamService ttsService;

    /**
     * 节点信息
     */
    private final ExecutorInfo executorInfo = new ExecutorInfo();

    /**
     * 计时信息
     */
    private final Map<String, MsgInfoTrack> msgInfoTrackMap = new HashMap<>();

    /**
     * 进入不响应状态
     */
    private AtomicBoolean silence = new AtomicBoolean(false);

    /**
     * AI 消息发送完成时间，开始等待访客说话
     */
    private Long aiSpeakEndTime = null;

    private final AtomicBoolean hasRtcDataToPush = new AtomicBoolean(false);

    /**
     * 性别
     */
    private String gender;
    private float genderMaxScore;
    private boolean genderAdded = false;


    @Override
    public void setEnvironment(Environment environment) {
        CallContextService.environment = environment;
    }


    @PostConstruct
    public void postConstruct() {
        if(environment == null) {
            this.isFsWebsocketReady = false;
            throw new RuntimeException("invalid environment, skip");
        }

        String fsPortStr = environment.getProperty("fsPort");
        String serverPort = environment.getProperty("server.port");

        MDC.put("server_port", serverPort);

        log.info("websocket init, fsPortStr {}, serverPort {}", fsPortStr, serverPort);
        if(StringUtils.isBlank(fsPortStr) || StringUtils.isBlank(serverPort)) {
            log.info("invalid websocket port configure, skip");
            //todo alarm
            this.isFsWebsocketReady = false;
            throw new RuntimeException("invalid websocket configure, skip");
        }

        try {
            executorInfo.setTelPort(Integer.parseInt(fsPortStr));
            executorInfo.setServerPort(Integer.parseInt(serverPort));
            String ip = calcIpAddr();
            if(StringUtils.isBlank(ip)) {
                log.error("fail to get local ip");
                throw new RuntimeException("invalid websocket configure, skip");
            }

            String s = hostMappingConfig.getHostMapping().get(ip);
            if(StringUtils.isNotBlank(s)) {
                executorInfo.setIp(s);
            } else {
                executorInfo.setIp(ip);
            }
        } catch (Exception e) {
            log.error("fail to postConstruct callContext {}", executorInfo);
            throw new RuntimeException("fail to postConstruct callContext , skip");
        }

        try {
            telWebSocketServer = new TelWebSocketServer(executorInfo.getTelPort(), this);
            telWebSocketServer.start();
        } catch (Exception e) {
            log.error("fail to start tel websocket server, roomId {}, port {}", task.getRoomId(), executorInfo);
            //todo alarm
            this.isFsWebsocketReady = false;
            throw new RuntimeException("invalid websocket configure, skip");
        }
    }

    /**
     * 开始呼叫
     * @param task 呼叫任务
     */
    public boolean startCall(CallTask task) {
        setWorking(true);
        this.task = task;

        boolean initRes = init(task);
        if(!initRes) {
            log.error("fail to start call, roomId {}", task.getRoomId());
            return false;
        }
        for (BufferService bufferService: bufferServices) {
            if(bufferService != null) {
                try{
                    bufferService.clean();
                }catch (Exception e) {
                    log.error("fail to clean buffer, roomId {}, service {}", task.getRoomId(), bufferService.getClass().getName());
                }
            }
        }
        return true;
    }

    public void sendWelcome() {
        // 标识呼叫实际开始
        markExecutorUsed();
        Constants.executor.submit(new Runnable() {
            public void run() {
                String welcomeSpeech = task.getCallConfig().getWelcomeSpeech();

                List<String> targets = new ArrayList<>();
                if(welcomeSpeech.contains("$$")) {
                    String[] splits = welcomeSpeech.split("\\$\\$");
                    targets.addAll(Arrays.asList(splits));
                } else {
                    targets.add(welcomeSpeech);
                }

                int i = 0;
                for (String target: targets) {
                    i++;
                    // 推送字幕
                    botSubtitleService.sendAIMsg(target, task.getRoomId(), task.getRoomId() + "welcome" + i);
                    chatContextService.saveMessage(task.getRoomId(), Message.ofAssistant(target));
                    log.info("sendWelcome, roomId {}, content {}", task.getRoomId(), target);
                    try {
                        Thread.sleep(1000);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }

                    target = target.replaceAll("hook", "").replaceAll("#", "");

                    if(i != targets.size()) {
                        getTtsService().call(target, task.getRoomId(), task.getRoomId() + "welcome" + i, false);
                        try {
                            Thread.sleep(1000);
                        } catch (InterruptedException e) {
                            throw new RuntimeException(e);
                        }
                    } else {
                        getTtsService().call(target, task.getRoomId(), task.getRoomId() + "welcome" + i, true);
                    }
                }

            }
        });
    }

    private String calcIpAddr(){
        try {
            // 获取所有网络接口的枚举
            Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
            while (interfaces.hasMoreElements()) {
                NetworkInterface networkInterface = interfaces.nextElement();
                // 过滤掉虚拟接口、未激活的接口
                if (networkInterface.isLoopback() || !networkInterface.isUp()) {
                    continue;
                }
                // 获取该网络接口的所有 IP 地址
                Enumeration<InetAddress> addresses = networkInterface.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    InetAddress address = addresses.nextElement();
                    if (address instanceof Inet4Address) {
                        // 过滤掉回环地址和链路本地地址
                        if (!address.isLoopbackAddress() && !address.isLinkLocalAddress()) {
                            return address.getHostAddress();
                        }
                    }
                }
            }
        } catch (SocketException e) {
            log.error("fail to calc ip addr", e);
        }
        return null;
    }

    /**
     * 初始化所有的服务
     */
    private boolean init(CallTask task) {
        this.welcomeComplete = false;
        this.welcomeSent = false;
        this.allowInterrupted = true;
        this.hookStatus = false;
        this.currentMsgId = "";
        this.silence.set(false);
        this.msgInfoTrackMap.clear();

        // 初始化sampleRate
        if(isTelChanel(task)) {
            task.setSampleRate(SAMPLE_RATE_8000);
        } else if(isRtcChannel(task)) {
            task.setSampleRate(SAMPLE_RATE_16000);
        } else if(isAgoraRtcChannel(task)) {
            task.setSampleRate(SAMPLE_RATE_16000);
        } else {
            log.error("invalid channel, channel {}, quit", task.getCallConfig().getChannel());
            return false;
        }

        // 默认允许打断
        if(vadConfig.getAllowInterrupt() != null) {
            this.allowInterrupted = vadConfig.getAllowInterrupt();
        }
        // 从任务中获取是否允许打断
        Boolean allowInterrupt = task.getCallConfig().getAllowInterrupt();
        if(allowInterrupt != null) {
            this.allowInterrupted = allowInterrupt;
        }

        log.info("init service begin, roomId {}, allowInterrupted {}", task.getRoomId(), allowInterrupted);

        List<TaskService> collect = taskServices.stream().sorted((o1, o2) -> o2.order() - o1.order()).collect(Collectors.toList());

        for (TaskService taskService: collect) {
            if(taskService != null) {
                boolean res = taskService.init(task);
                if(!res) {
                    log.error("fail to init service, roomId {}, service {}", task.getRoomId(), taskService.getClass().getName());
                    return false;
                }
            }
        }

        if(task.getCallConfig().getAsr().getVendor().equals(Constants.ASR_VENDOR_VOLCAN)) {
            asrService = volcanAsrService;
        }else if(task.getCallConfig().getAsr().getVendor().equals(Constants.ASR_VENDOR_TENCENT)) {
            asrService =  tencentAsrService;
        }

        if(task.getCallConfig().getTts().getVendor().equals(Constants.TTS_VENDOR_COSYVOICE)) {
            ttsService = cosyVoiceStreamService;
        }else if(task.getCallConfig().getTts().getVendor().equals(Constants.TTS_VENDOR_VOLCAN)) {
            ttsService =  volcanTTSStreamService;
        } else if(task.getCallConfig().getTts().getVendor().equals(Constants.TTS_VENDOR_MINIMAX)) {
            ttsService = minimaxTTSStreamService;
        }

        log.info("call_service_init complete, roomId {}, executor_node {}, executor_port {}", task.getRoomId(), executorInfo.getIp(), executorInfo.getServerPort());
        return true;
    }

    public void interrupt() {
        getTtsToRtcBuffer().clean();
        if(isAgoraRtcChannel(task)) {
            agoraRtcService.interrupt();
        }
    }

    /**
     * 清理所有的服务
     */
    public void clean(String inputRoomId) {
        if(task == null) {
            log.error("task is null, skip clean, roomId {}", inputRoomId);
            return;
        }

        String roomId = task.getRoomId();

        if(!roomId.equals(inputRoomId)) {
            log.warn("clean service roomId not match, roomId {}, inputRoomId {}, quit", roomId, inputRoomId);
            return;
        }

        log.info("clean service begin roomId {}", roomId);

        setWorking(false);

        for (TaskService taskService: taskServices) {
            if(taskService != null) {
                try{
                    taskService.clean();
                }catch (Exception e) {
                    log.error("fail to clean service, roomId {}, service {}", roomId, taskService.getClass().getName());
                }
            }
        }
        for (BufferService bufferService: bufferServices) {
            if(bufferService != null) {
                try{
                    bufferService.clean();
                }catch (Exception e) {
                    log.error("fail to clean buffer, roomId {}, service {}", roomId, bufferService.getClass().getName());
                }
            }
        }

        if(telWebSocketServer != null) {
            telWebSocketServer.clean();
        }
        log.info("clean service complete, roomId {}", roomId);

        Set<String> msgIds = msgInfoTrackMap.keySet();
        for (String msgId: msgIds) {
            log.info("roomId {}, msgId {}, timer: {}", roomId, msgId, msgInfoTrackMap.get(msgId));
        }

        this.msgInfoTrackMap.clear();
        this.task = null;
        this.welcomeComplete = false;
        this.welcomeSent = false;
        this.allowInterrupted = true;
        this.currentMsgId = "";
        this.hookStatus = false;
        this.silence.set(false);
        this.gender = null;
        this.genderMaxScore = 0;
        this.genderAdded = false;
    }

    /**
     * 统一主动挂机入口
     * @param roomId 房间号
     * @param channel tel,rtc
     * @param bizTime 业务时间
     */
    public void hangupManual(String roomId, String channel, long bizTime) {
        log.info("hangup auto, roomId {}, channel {}", roomId, channel);
        try{
            // 通过gateway挂机，清理
            getInfoReportService().hangup(roomId, channel, bizTime);
        }catch (Exception e) {
            log.error("fail to hangup, roomId {}, channel {}", roomId, channel, e);
        }finally {
            new Thread(() -> {
                clean(getTask().getRoomId());
            }).start();
        }
    }

    public boolean isInterrupted(String msgId) {
        MsgInfoTrack msgInfoTrack = msgInfoTrackMap.get(msgId);
        if(msgInfoTrack == null) {
            return false;
        }
        return msgInfoTrack.isInterrupted();
    }

    private void setWorking(boolean working) {
        isWorking = working;
        // 工作状态变化会自动上报
        infoReportService.reportInfo();
    }

    public boolean isCurrentMsgId(String msgId) {
        if(StringUtils.isBlank(currentMsgId)) {
            return true;
        }
        return this.currentMsgId.equals(msgId);
    }

    public int getChatHistoryLength() {
        if(task.getCallConfig().getLlm().getHistoryLength()!=null) {
            return task.getCallConfig().getLlm().getHistoryLength();
        }
        return llmConfig.getHistoryLength();
    }

    public boolean isAgoraRtcChannel(CallTask task){
        String channel = task.getCallConfig().getChannel();
        return StringUtils.isNotBlank(channel) && channel.equalsIgnoreCase("agora_rtc");
    }

    public boolean isRtcChannel(CallTask task){
        String channel = task.getCallConfig().getChannel();
        return StringUtils.isNotBlank(channel) && channel.equalsIgnoreCase("rtc");
    }

    public boolean isTelChanel(CallTask task){
        String channel = task.getCallConfig().getChannel();
        return StringUtils.isNotBlank(channel) && channel.equalsIgnoreCase("tel");
    }

    public void setMsgInfoTrack(String msgId, String type, String msg) {
        if(StringUtils.isBlank(msgId)) {
            return;
        }

        if(StringUtils.isBlank(msg)) {
            return;
        }

        MsgInfoTrack msgInfoTrack = msgInfoTrackMap.get(msgId);
        if(msgInfoTrack == null) {
            return;
        }

        if(type.equalsIgnoreCase("vad_complete_msg")) {
            msgInfoTrack.setVadCompleteMsg(msg);
        }

        if(type.equalsIgnoreCase("asrFinalMsg")) {
            msgInfoTrack.setAsrFinalMsg(msg);
        }

        if(type.equalsIgnoreCase("volcanAsrFinalMsg")) {
            msgInfoTrack.setVolcanAsrFinalMsg(msg);
        }
    }

    public MsgInfoTrack getMsgInfoTrack(String msgId) {
        if(StringUtils.isBlank(msgId)) {
            return null;
        }
        return msgInfoTrackMap.get(msgId);
    }

    public void setMsgInfoTrack(String msgId, String type, long time) {
        if(StringUtils.isBlank(msgId)) {
            return;
        }

        MsgInfoTrack msgInfoTrack = msgInfoTrackMap.get(msgId);
        if(msgInfoTrack == null) {
            msgInfoTrack = new MsgInfoTrack(msgId);
            msgInfoTrackMap.put(msgId, msgInfoTrack);
        }

        if(type.equalsIgnoreCase("interrupt")) {
            msgInfoTrack.setInterrupted(true);
        }

        // 开口时间
        if(type.equalsIgnoreCase("speak")) {
            if(msgInfoTrack.getSpeakTime() == null) {
                msgInfoTrack.setSpeakTime(time);
            }
        }

        if(type.equalsIgnoreCase("speak_end")) {
            msgInfoTrack.setSpeakEndTime(time);
        }

        if(type.equalsIgnoreCase("speak_complete")) {
            if(msgInfoTrack.getSpeakCompleteTime() == null) {
                msgInfoTrack.setSpeakCompleteTime(time);
            }
        }

        if(type.equalsIgnoreCase("speak_duration")) {
            if(msgInfoTrack.getSpeakDuration() == null) {
                msgInfoTrack.setSpeakDuration(time);
            }
        }

        if(type.equalsIgnoreCase("asr_first_token")) {
            if(msgInfoTrack.getAsrFirstTokenTime() == null) {
                msgInfoTrack.setAsrFirstTokenTime(time);
            }
        }

        if(type.equalsIgnoreCase("volcan_asr_first_token")) {
            if(msgInfoTrack.getVolcanAsrFirstTokenTime() == null) {
                msgInfoTrack.setVolcanAsrFirstTokenTime(time);
            }
        }

        if(type.equalsIgnoreCase("send_asr_data_complete")) {
            if(msgInfoTrack.getSentAsrCompleteTime() == null) {
                msgInfoTrack.setSentAsrCompleteTime(time);
            }
        }

        if(type.equalsIgnoreCase("send_volcan_asr_data_complete")) {
            if(msgInfoTrack.getSentVolcanAsrCompleteTime() == null) {
                msgInfoTrack.setSentVolcanAsrCompleteTime(time);
            }
        }

        if(type.equalsIgnoreCase("asr_complete")) {
            if(msgInfoTrack.getAsrCompleteTime() == null) {
                msgInfoTrack.setAsrCompleteTime(time);
            }
        }

        if(type.equalsIgnoreCase("volcan_asr_complete")) {
            if(msgInfoTrack.getVolcanAsrCompleteTime() == null) {
                msgInfoTrack.setVolcanAsrCompleteTime(time);
            }
        }

        if(type.equalsIgnoreCase("knowledge")) {
            if(msgInfoTrack.getKnowledgeTime() == null) {
                msgInfoTrack.setKnowledgeTime(time);
            }
        }

        if(type.equalsIgnoreCase("llm_begin")) {
            if(msgInfoTrack.getLlmBeginTime() == null) {
                msgInfoTrack.setLlmBeginTime(time);
            }
        }

        if(type.equalsIgnoreCase("llm_first_token")) {
            if(msgInfoTrack.getLlmFirstTokenTime() == null){
                msgInfoTrack.setLlmFirstTokenTime(time);
            }
        }

        if(type.equalsIgnoreCase("tts_first_token")) {
            if(msgInfoTrack.getTtsFirstTokenTime() == null) {
                msgInfoTrack.setTtsFirstTokenTime(time);
            }
        }
    }

    public void setSilence(boolean silence, String reason, String msgId) {
        log.info("set silence {}, reason: {}, roomId: {}, msgId {}", silence, reason, task.getRoomId(), msgId);
        this.silence.set(silence);
    }

    public boolean isSilence(){
        return this.silence.get();
    }

    private void markExecutorUsed() {
        dogService.markExecutorUsed();
    }

    public void saveEmotion(String msgId, String emotion, float score) {
        MsgInfoTrack msgInfoTrack = msgInfoTrackMap.get(msgId);
        if(msgInfoTrack == null) {
            return;
        }
        if("angry".equalsIgnoreCase(emotion)) {
            log.info("current emotion {}, score {}, roomId {}, msgId {}", emotion, score, task.getRoomId(), msgId);
            msgInfoTrack.setEmotion(emotion);
        }
    }

    public void saveGender(String msgId, String gender, float score) {
        if(StringUtils.isBlank(gender)) {
            log.info("set gender {}, score {}, roomId {}, msgId {}", gender, score, task.getRoomId(), msgId);
            this.gender = gender;
            this.genderMaxScore = score;
            return;
        }

        if(score > genderMaxScore) {
            log.info("update gender {}, score {}, roomId {}, msgId {}", gender, score, task.getRoomId(), msgId);
            this.gender = gender;
            this.genderMaxScore = score;
        }
    }

    /**
     * 是否检测性别, 节省asr费用
     * key: gender_check, true or false
     * @return
     */
    public boolean isCheckGender() {
        JSONObject extra = task.getExtra();
        if(extra == null) {
            return false;
        }
        return extra.getBoolean("gender_check");
    }

    /**
     * 获取性别描述，key：male or female
     * @return
     */
    public String genderDesc(){
        if(this.genderMaxScore < 0.7){
            return null;
        }
        JSONObject extra = task.getExtra();
        if(extra == null) {
            return null;
        }
        return extra.getString(this.gender);
    }

    /**
     * 判断当前消息id对应ai回复是否完成输出到rtc
     * @return
     */
    public boolean isAiSpeechEnd() {
        MsgInfoTrack msgInfoTrack = getMsgInfoTrack(getCurrentMsgId());
        if(msgInfoTrack == null) {
            return true;
        }

        if(msgInfoTrack.isInterrupted()) {
            return true;
        }

        return msgInfoTrack.isAiSpeechEnd();
    }
}
