package com.mantis.rtc.service.llm;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.mantis.micor.pojo.CallConfig;
import com.mantis.micor.pojo.CallTask;
import com.mantis.rtc.config.LLMConfig;
import com.mantis.rtc.pojo.Constants;
import com.mantis.rtc.pojo.chat.*;
import com.mantis.rtc.service.CallContextService;
import com.mantis.rtc.service.TaskService;
import com.mantis.rtc.utils.Utils;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

import static com.mantis.rtc.pojo.Constants.JSON_MEDIA_TYPE;
import static com.mantis.rtc.utils.Utils.calcRandomWeiRes;

@Service
@Slf4j
public class LLMService implements TaskService {
    public static final String LLM_VOLCAN = "volcan";
    public static final String LLM_TENCENT = "tencent";
    public static final String LLM_THETURBO = "theturbo";
    public static final String LLM_DEEPSEEK = "deepseek";
    @Autowired
    private CallContextService contextService;

    @Autowired
    private ChatContextServiceMemoryImpl chatContextService;

    @Autowired
    private BotSubtitleService botSubtitleService;

    @Autowired
    private LLMConfig llmConfig;

    @Autowired
    @Qualifier("okhttpClient")
    private OkHttpClient client;

//    @Autowired
//    @Qualifier("tencentKnowledgeServiceImpl")
//    private KnowledgeService tencentKnowledgeServiceImpl;

    @Autowired
    @Qualifier("volcanKnowledgeServiceImpl")
    private KnowledgeService volcanKnowledgeServiceImpl;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    private KnowledgeService knowledgeService;

    private final ScheduledExecutorService executor = Executors.newScheduledThreadPool(1);

    /**
     * 大模型调用信息，本项目负责维护，包括url与AK
     */
    private LLMConfig.LLMInfo llmCallInfo = null;
    /**
     * 豆包缓存key
     */
    private String douBaoContextKey;
    private String roomId = null;
    private CallTask task = null;

    /**
     * LLM client
     */
    private ChatGPTStream chatGPTStream = null;
    private String model;


    @Override
    public boolean init(CallTask task) {
        this.task = task;
        this.roomId = task.getRoomId();

        CallConfig.Knowledge knowledge = task.getCallConfig().getKnowledge();
        // 默认使用腾讯
        if(knowledge == null || StringUtils.isBlank(knowledge.getVendor())) {
            log.info("knowledge config is invalid, skip, roomId {}", roomId);
            knowledgeService = null;
        } else if(knowledge.getVendor().equalsIgnoreCase("volcan")) {
            knowledgeService = volcanKnowledgeServiceImpl;
        }

        CallConfig.LLM llmReq = task.getCallConfig().getLlm();
        if (llmReq == null) {
            log.error("llmReq config is invalid, roomId {}", roomId);
            return false;
        }

        String systemPrompt = llmReq.getSystemPrompt();
        if (StringUtils.isBlank(systemPrompt)) {
            log.error("llmReq systemPrompt is invalid, roomId {}", roomId);
            return false;
        }

        model = llmReq.getModel();
        if (StringUtils.isBlank(model)) {
            log.error("llmReq model is invalid, roomId {}", roomId);
            return false;
        }


        String vendor = llmReq.getVendor();

        if(StringUtils.isBlank(vendor)) {
            log.error("llmReq vendor is invalid, roomId {}", roomId);
            return false;
        }

        // 根据请求中的大模型厂商来获取调用地址与apiKey
        if(vendor.equals(LLM_DEEPSEEK)) {
            llmCallInfo = llmConfig.getDeepseek();
        } else if(vendor.equals(LLM_THETURBO)) {
            llmCallInfo = llmConfig.getTheturbo();
        } else if(vendor.equals(LLM_TENCENT)) {
            llmCallInfo = llmConfig.getTencent();
        } else if(vendor.equals(LLM_VOLCAN)) {
            llmCallInfo = llmConfig.getVolcan();
        }

        if(llmCallInfo == null) {
            log.error("llmReq vendor is invalid, roomId {}", roomId);
            return false;
        }

        // 从配置信息中获取调用URL和apiKey
        String url = llmCallInfo.getUrl();
        if (StringUtils.isBlank(url)) {
            log.error("llmReq url is invalid, roomId {}", roomId);
            return false;
        }

        String apiKey = llmCallInfo.getApiKey();

        // 如果输入带了llm，那么使用输入的
        if(StringUtils.isNotBlank(llmReq.getApiKey())) {
            apiKey = llmReq.getApiKey();
        }

        if (StringUtils.isBlank(apiKey)) {
            log.error("llmReq apiKey is invalid, roomId {}", roomId);
            return false;
        }

        if(vendor.equals(LLM_VOLCAN)) {
            // 如果是reponse API
            if(llmReq.getResponse().equals(Boolean.TRUE)) {
                url = url + "/responses";
                douBaoContextKey = fetchResponseContextKey(url, model, apiKey, systemPrompt);
            } else {
                // 豆包chat API 创建上下文
                douBaoContextKey = fetchDouBaoContextKey(url, model, apiKey, systemPrompt);
                url = url+ "/context/chat/completions";
            }
        }

        // 如果用户有自定义的prompt，则保存到上下文
        String userPrompt = llmReq.getUserPrompt();
        if(StringUtils.isNotBlank(userPrompt)) {
            chatContextService.saveMessage(roomId, Message.ofSystem(userPrompt));
        }

        // 初始化chatGPTStream
        chatGPTStream = ChatGPTStream.builder()
                .timeout(600)
                .apiKey(apiKey)
                .url(url)
                .build()
                .init();
        return true;
    }

    private String fetchContextKeyFromRedis(String systemPrompt, String model) {
        String key = calcContextKey(systemPrompt, model);
        ValueOperations<String, String> valueOperations = redisTemplate.opsForValue();
        return valueOperations.get(key);
    }

    private void saveContextKey(String systemPrompt, String model, String contextId) {
        ValueOperations<String, String> valueOperations = redisTemplate.opsForValue();
        String key = calcContextKey(systemPrompt, model);
        valueOperations.set(key, contextId);
        redisTemplate.expire(key, 1, TimeUnit.DAYS);
    }

    private static String calcContextKey(String systemPrompt, String model) {
        // 计算systemPrompt的md5
        String md5Key = Utils.getChineseStringMD5Key(systemPrompt);
        return String.format("context_key:%s:%s",model, md5Key);
    }

    private String fetchDouBaoContextKey(String url, String model, String apiKey, String systemPrompt) {
        String contextId = fetchContextKeyFromRedis(systemPrompt, model);
        if(StringUtils.isNotBlank(contextId)) {
            log.info("fetch doubao context key from redis, roomId {}, model {}, contextId {}", roomId, model, contextId);
            return contextId;
        }

        contextId = fetchContextKeyInner(url, model, apiKey, systemPrompt);
        if(StringUtils.isNotBlank(contextId)) {
            saveContextKey(systemPrompt, model, contextId);
        }
        return contextId;
    }

    private String fetchContextKeyInner(String url, String model, String apiKey, String systemPrompt) {
        log.info("fetch doubao context key from doubao, roomId {}, model {}", roomId, model);

        String newUrl = url + "/context/create";
        // system prompt
        JSONArray array = new JSONArray();
        array.add(new JSONObject().fluentPut("role", "system").fluentPut("content", systemPrompt));
        // 30小时
        JSONObject req = new JSONObject().fluentPut("model", model).fluentPut("ttl", 30*3600).fluentPut("mode", "common_prefix").fluentPut("messages", array);

        RequestBody requestBody = RequestBody.create(JSON_MEDIA_TYPE, req.toJSONString());
        Request request = new Request.Builder()
                .url(newUrl)
                .post(requestBody)
                .header("Authorization", "Bearer " + apiKey )
                .build();

        try (Response response = client.newCall(request).execute()) {
            if(!response.isSuccessful()) {
                log.error("fail to fetchDouBaoContextKey, url {}, response code {}", newUrl, response.code());
                return null;
            }

            ResponseBody body = response.body();
            if(body != null) {
                String resp = body.string();
                log.info("fetch douBao context key from doubao, roomId {}, model {}, response {}", roomId, model, resp);
                JSONObject jsonObject = JSON.parseObject(resp, JSONObject.class);
                if(jsonObject != null) {
                    return jsonObject.getString("id");
                }
            }
        }catch (IOException e) {
            log.error("exception when fetchDouBaoContextKey, url {}, roomId {}", newUrl, roomId, e);
        }
        return null;
    }

    private String fetchResponseContextKey(String url, String model, String apiKey, String systemPrompt) {
        String contextId = fetchContextKeyFromRedis(systemPrompt, model);
        if(StringUtils.isNotBlank(contextId)) {
            log.info("fetch doubao response context key from redis, roomId {}, model {}, contextId {}", roomId, model, contextId);
            return contextId;
        }

        contextId = fetchResponseContextKeyInner(url, model, apiKey, systemPrompt);
        if(StringUtils.isNotBlank(contextId)) {
            saveContextKey(systemPrompt, model, contextId);
        }
        return contextId;
    }

    private String fetchResponseContextKeyInner(String url, String model, String apiKey, String systemPrompt) {
        log.info("fetch doubao response context key from doubao, roomId {}, model {}", roomId, model);
        // system prompt
        JSONArray array = new JSONArray();
        array.add(new JSONObject().fluentPut("role", "system").fluentPut("content", systemPrompt));

        JSONObject cache = new JSONObject();
        cache.put("type", "enabled");

        JSONObject thinking = new JSONObject();
        thinking.put("type", "disabled");
        // 30小时
        JSONObject req = new JSONObject().fluentPut("model", model).fluentPut("input", array).fluentPut("caching", cache).fluentPut("thinking", thinking);

        RequestBody requestBody = RequestBody.create(JSON_MEDIA_TYPE, req.toJSONString());
        Request request = new Request.Builder()
                .url(url)
                .post(requestBody)
                .header("Authorization", "Bearer " + apiKey )
                .build();

        try (Response response = client.newCall(request).execute()) {
            if(!response.isSuccessful()) {
                log.error("fail to fetchDouBaoContextKey, url {}, response code {}", url, response.code());
                return null;
            }

            ResponseBody body = response.body();
            if(body != null) {
                String resp = body.string();
                log.info("fetch douBao context key from doubao, roomId {}, model {}, response {}", roomId, model, resp);
                JSONObject jsonObject = JSON.parseObject(resp, JSONObject.class);
                if(jsonObject != null) {
                    return jsonObject.getString("id");
                }
            }
        }catch (IOException e) {
            log.error("exception when fetchDouBaoContextKey, url {}, roomId {}", url, roomId, e);
        }
        return null;
    }

    @Override
    public void clean() {
        this.roomId = null;
        this.douBaoContextKey = null;
        this.llmCallInfo = null;
    }

    @Override
    public int order() {
        return 3;
    }

    /**
     * 尽可能记录访客的消息，尽可能放入消息上下文， 除非是无意义的打招呼部分
     * @param msgId
     * @param query
     */
    public void doLLm(String msgId, String query) {
        log.info("do llm, roomId {}, msgId {}, latestMsg {}", roomId, msgId, query);
        MsgInfoTrack msgInfoTrack = contextService.getMsgInfoTrack(msgId);

        String desc = contextService.genderDesc();

        if (!StringUtils.isBlank(desc) && !contextService.isGenderAdded()) {
            log.info("add gender desc to system prompt, roomId {}, msgId {}, desc {}", roomId, msgId, desc);
            chatContextService.saveMessage(roomId,  Message.ofSystem("请称呼对方为:" + desc));
            contextService.setGenderAdded(true);
        }

        if(!msgInfoTrack.isValidMsg()) {
            log.info("do llm ,but msgId is invalid, skip, roomId {}, msgId {}", roomId, msgId);
            return;
        }

        if (StringUtils.isBlank(query)) {
            log.error("asr result is empty, msgId {}, roomId {}", roomId, msgId);
            Long speakDuration = msgInfoTrack.getSpeakDuration();

            // 如果说话持续时间小于2秒, 且asr结果为空，则不调用大模型
            if(speakDuration != null && speakDuration < 2000) {
                // 停止静默
                contextService.setSilence(false, "speak duration is too short", msgId);
                log.info("asr result is empty and speak duration is too short, skip, roomId {}, msgId {}, speakDuration {}", roomId, msgId, speakDuration);
                // 标识AI说话完毕，启动追问
                contextService.setAiSpeakEndTime(System.currentTimeMillis());
                return;
            }

            // 立即回复中性内容
            executor.schedule(() -> {
                String res = calcRandomWeiRes(Constants.EMPTY_ASR_RES);
                contextService.getTtsService().call(res,roomId, msgId, true);
                botSubtitleService.sendAIMsg(res, roomId, msgId);
            }, 500, TimeUnit.MILLISECONDS);
            return;
        }

        // 对简单消息做拦截处理
        SimpleResponse sr = handleSimpleMsg(query, roomId);

        // 不需要送给AI了, 也不用记录到对话历史
        if (null != sr && StringUtils.isNotBlank(sr.getToUser()) && !sr.isSendLLm()) {
            log.info("query is simple msg, skip llm, roomId {}, msgId {}, query {}, response {}", roomId, msgId, query, sr.getToUser());
            executor.schedule(() -> {
                contextService.getTtsService().call(sr.getToUser(),roomId, msgId, true);
                botSubtitleService.sendAIMsg(sr.getToUser(), roomId, msgId);
            }, 500, TimeUnit.MILLISECONDS);
            return;
        }

        // 处理访客消息：获取上一轮的回复
        saveLastRoundAIMsg(query, roomId, msgId);

        // 写入本轮消息Id到redis
        chatContextService.saveLatestMsgId(roomId, msgId);
        // 保存本轮访客消息到redis
        chatContextService.saveMessage(roomId, Message.of(query));
        // 访客消息字幕推送
        if (StringUtils.isNotEmpty(query)) {
            botSubtitleService.sendUserMsg(query, roomId, msgId);
        }

        // 送大模型前，先播放语气词，减少延迟的感觉
//        executor.schedule(() -> {
//            contextService.getTtsService().call(calcRandomWeiRes(Constants.SIMPLE_RES2.keySet().toArray(new String[0])),roomId, msgId, true);
//        }, 500, TimeUnit.MILLISECONDS);

        // 查找知识库
        String question = null;
        String answer = null;
        // 如果没有简单返回的结果，开始匹配知识库
        if(query.length() >=5 && knowledgeService != null) {
            Map<String, String> knowledge = knowledgeService.searchKnowledge(task, query, msgId);
            if (knowledge != null && knowledge.size() > 0) {
                Set<String> keys = knowledge.keySet();
                for (String key : keys) {
                    question = key;
                    answer = knowledge.get(key);
                }
            }
        }

        // 追加RAG，如果没有答案，那就不要追加, 克隆历史消息，而不是直接使用，否则rag也会进入消息历史
        List<Message> reqMessages = null;
        if (StringUtils.isNotBlank(answer)) {
            reqMessages = chatContextService.cloneMessages(roomId, null);
            reqMessages.add(Message.ofSystem("请参考以下信息回答用户问题：\n" +
                                             "问题：" + question + "\n" +
                                             "答案：" + answer + "\n" ));
        } else {
            reqMessages = chatContextService.getChatHistory();
        }

        CallConfig.LLM llm = contextService.getTask().getCallConfig().getLlm();
        Float temperature = llm.getTemperature();
        if (temperature == null) {
            temperature = llmConfig.getTemperature();
        }

        Float frequencyPenalty = llm.getFrequencyPenalty();

        // 监听大模型回复
        SseStreamListener listener = new SseStreamListener(chatContextService, contextService, roomId, msgId, query);
        listener.setOnComplete(latestResponse -> {
            String result = latestResponse.replaceAll("\\n", "");
            // add ai message to bot-subtitle
            if (StringUtils.isNotEmpty(result)) {
                log.info("llm complete, roomId {}, msgId {}, 本次回复的内容: {}", roomId, msgId, result);
                botSubtitleService.sendAIMsg(result, roomId, msgId);
                // 大模型返回了存json，需要触发追问
                if(result.startsWith("{") && result.endsWith("}")) {
                    // 停止静默
                    contextService.setSilence(false, "ai response pure json", msgId);
                    log.info("ai response pure json, roomId {}, msgId {}, content {}", roomId, msgId, result);
                    // 标识AI说话完毕，启动追问
                    contextService.setAiSpeakEndTime(System.currentTimeMillis());
                }
            }
        });

        contextService.setMsgInfoTrack(msgId, "llm_begin", System.currentTimeMillis());

        log.info("call llm, roomId {}, vendor {}, Response API {}", roomId, llm.getVendor(), llm.getResponse());
        if(llm.getVendor().equals(LLM_VOLCAN)){
            if(llm.getResponse().equals(Boolean.TRUE)) {
                Thinking thinking = Thinking.builder().type("disabled").build();
                ResponseCompletion responseCompletion = ResponseCompletion.builder()
                        .model(this.model)
                        .previous_response_id(douBaoContextKey)
                        .input(reqMessages)
                        .thinking(thinking)
                        .temperature(temperature)
                        .stream(true)
                        .build();
                chatGPTStream.streamChatCompletion(roomId, msgId, responseCompletion, listener);
            } else {
                ChatCompletion chatCompletion = ChatCompletion.builder()
                        .model(this.model)
                        .contextId(douBaoContextKey)
                        .temperature(temperature)
                        .frequencyPenalty(frequencyPenalty)
                        .messages(reqMessages)
                        .stream(true)
                        .streamOption(new StreamOption(true))
                        .build();
                chatGPTStream.streamChatCompletion(roomId, msgId, chatCompletion, listener);
            }
        } else {
            ChatCompletion chatCompletion = ChatCompletion.builder()
                    .model(this.model)
                    .temperature(temperature)
                    .frequencyPenalty(frequencyPenalty)
                    .messages(reqMessages)
                    .stream(true)
                    .streamOption(new StreamOption(true))
                    .build();
            chatGPTStream.streamChatCompletion(roomId, msgId, chatCompletion, listener);
        }
    }

    /**
     * 处理RAG
     * @param reqMessages 对话历史
     * @param query 本轮消息
     * @param answer 根据本轮消息从知识库获取到的答案
     */
    private void appendRAG(List<Message> reqMessages, String query, String answer) {
        if (reqMessages == null || reqMessages.size() == 0) {
            return;
        }
        // 如果没有答案，那就不要追加
        if (StringUtils.isBlank(answer)) {
            return;
        }
        // 定位system prompt
        for (Message message : reqMessages) {
            if ("system".equalsIgnoreCase(message.getRole())) {
                StringBuilder sb = new StringBuilder();
                sb.append(message.getContent());
                if (StringUtils.isNotBlank(query)) {
                    sb.append("/n ## 请参考FAQ回复：");
                    sb.append("- 问题：").append(query).append("/n").append("- 回复：").append(answer).append("/n");
                } else {
                    sb.append("/n ## 请参考知识回复：").append(answer).append("/n");
                }
                message.setContent(sb.toString());
                break;
            }
        }
    }

    /**
     * 处理简单消息，有些场景下不需要调用大模型，可以直接回复
     * @param latestMsg 本轮消息
     * @param roomId 房间号
     * @return
     */
    private SimpleResponse handleSimpleMsg(String latestMsg, String roomId) {
        // 开场重复的喂，会导致AI无法说话
        String s = latestMsg.replaceAll("[！!，,。.?？]", "");
        if (s.equals("喂") || s.equals("在吗") || s.equals("能听见吗") || s.equals("能听到吗")) {
            
            return SimpleResponse.builder().sendLLm(false).toUser(calcRandomWeiRes(Constants.SIMPLE_RES)).build();
        }
        return null;
    }

    /**
     * 获取上一轮AI的回复放入上下文
     * @param latestMsg 本轮消息
     * @param roomId 房间号
     * @param msgId 消息id
     */
    private void saveLastRoundAIMsg(String latestMsg, String roomId, String msgId) {

        // 处理本轮访客消息之前，首先获取上一轮访客消息Id
        String msgIdBefore = chatContextService.getLatestMsgId(roomId);
        log.debug("roomId {}, fetch last round msgId {}", roomId, msgIdBefore);

        // 获取上一轮AI的回复
        String aiMsg = null;
        // 根据消息Id查找对应的AI回复消息，保存到消息上下文
        if (StringUtils.isNotBlank(msgIdBefore)) {
            aiMsg = chatContextService.getAIMsg(msgIdBefore);
            log.debug("found last round AI message, roomId {}, content {}", roomId, aiMsg);
        }

        // 保存上轮AI回复
        if (StringUtils.isNotBlank(aiMsg)) {
            log.debug("save last round ai msg, roomId {}, content {}", roomId, aiMsg);
            chatContextService.saveMessage(roomId, Message.ofAssistant(aiMsg));
        }
    }
}
