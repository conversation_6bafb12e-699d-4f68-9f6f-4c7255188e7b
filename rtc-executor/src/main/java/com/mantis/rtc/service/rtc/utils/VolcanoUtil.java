package com.mantis.rtc.service.rtc.utils;

import lombok.NonNull;

public final class VolcanoUtil {

    /**
     * 使用 Token 完成鉴权
     *
     * <a href="https://www.volcengine.com/docs/6348/70121">文档</a>
     */
    public static String getRtcToken(@NonNull String appId, @NonNull String appKey, @NonNull String roomId, @NonNull String userId) {
        AccessToken token = new AccessToken(appId, appKey, roomId, userId);
        // 填0，token有效期为无限期有效。而用户在房间内最大时长是72小时。
        token.ExpireTime(0);
        token.AddPrivilege(AccessToken.Privileges.PrivSubscribeStream, 0);
        token.AddPrivilege(AccessToken.Privileges.PrivPublishStream, 0);
        return token.Serialize();
    }
}