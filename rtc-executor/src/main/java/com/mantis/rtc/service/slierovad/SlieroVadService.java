package com.mantis.rtc.service.slierovad;

import ai.onnxruntime.OrtException;
import com.mantis.micor.pojo.CallConfig;
import com.mantis.micor.pojo.CallTask;
import com.mantis.rtc.config.VadConfig;
import com.mantis.rtc.pojo.Constants;
import com.mantis.rtc.service.CallContextService;
import com.mantis.rtc.service.TaskService;
import com.mantis.rtc.service.llm.BotSubtitleService;
import com.mantis.rtc.utils.Utils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * https://devcloud.cn-north-4.huaweicloud.com/codeartside/webide?project_id=1464719&file_path=example%2Fsrc%2Fmain%2Fjava%2Fcom%2Fkonovalov%2Fvad%2Fexample%2FVadSileroFragment.kt&branch=main&authorization=eyJhbGciOiJIUzUxMiJ9.eyJqdGkiOiI2NmU3YTk2MDhhNzY0ZjM2ZDc0NzE2OWUiLCJzdWIiOiJxcV80MjI3NDMxMyIsImF1dGhvcml0aWVzIjpbXSwib2JqZWN0SWQiOiI2NzRhN2VkNGJmNjRlNjY5MDgyNzc1MGUiLCJpYXQiOjE3MzI5MzUzODAsImV4cCI6MTczNDY4MjMxMX0.tx-GEY4MdHiTpAUZ7tOqjXA1Vls39I_jJL9PPZ9YwDiur95oETpZFkTFVsAgD0QcG-L0u3GjGQAD1tuNbLGOkg
 */
@Slf4j
@Service
public class SlieroVadService implements TaskService {
    @Autowired
    private CallContextService contextService;

    @Autowired
    private VadConfig config;

    @Autowired
    private BotSubtitleService botSubtitleService;

    //  ------------------ 常量定义开始  ----------------------------------
    private static final String MODEL_PATH = "/mantis/rtc-service/silero_vad.onnx";
    /**
     * 人声阈值
     */
    private static final float START_THRESHOLD = 0.5f;
    /**
     * 静音阈值
     */
    private static final float END_THRESHOLD = START_THRESHOLD - 0.15f;

    /**
     * 如果静音时间超过MIN_SILENCE_DURATION_MS， 判定为语音结束
     */
    private static final int MIN_SILENCE_DURATION_MS = 1100;
    /**
     * 最大说话时长，防止噪音连续打断
     */
    private static final int MAX_SPEECH_DURATION_MS = 5000;

    /**
     * The minimum duration in milliseconds for speech segments，
     * The value of this parameter will define the necessary and sufficient duration of positive results to recognize result as speech
     */
    private static final int MIN_SPEECH_DURATION_MS = 500;

    // ------------------ 变量定义开始 -------------------------------------------

    private SlieroVadOnnxModel model;

    /**
     * Minimum number of silence samples to determine the end threshold of speech
     * 最小静音采样数，用于触发人声结束
     */
    private float minSilenceSamples;

    /**
     * 最大说话持续的采样数
     */
    private float maxSpeechSamples;

    /**
     * 最小人声采样数，低于这个值不认为开始说话
     */
    private float minInterruptSpeechSamples;

    // Whether in the triggered state (i.e. whether speech is being detected)
    private boolean triggered;

    // Temporarily stored number of speech end samples 临时停止说话的位置
    private long speech_end_pos;

    // 开始说话的位置
    private long speechBegin = 0;

    // Number of samples currently being processed, 开始说话的采样数？
    private long currentSamples;

    /**
     * vad 可用状态，是否初始化完成
     */
    private boolean vadStatus = false;

    private CallTask task = null;
    private String roomId = "";

    /**
     * 对话分隔，每次开始说话产生一个消息Id
     */
    private String msgId = "";

    private int sampleRate = 16000;


    /**
     * 本次消息是否有效
     */
    private boolean msgIssued = false;

    /**
     * 持续说话的采样数
     */
    private long speech_segment_duration = 0;

    /**
     * 累计追问次数
     */
    private int wakeupCount = 0;

    private final ScheduledExecutorService executor = Executors.newScheduledThreadPool(1);


    @PostConstruct
    public void initVadDetector() {
        try {
            this.model = new SlieroVadOnnxModel(MODEL_PATH);
        } catch (OrtException e) {
            log.error("fail to init vad model", e);
            this.vadStatus = false;
            return;
        }

        this.vadStatus = true;
    }

    @Override
    public boolean init(CallTask task) {
        this.task = task;
        if (!this.vadStatus) {
            log.error("vad init model fail, roomId {}", task.getRoomId());
            return false;
        }
        this.sampleRate = task.getSampleRate();


        int min_silence_time = MIN_SILENCE_DURATION_MS;
        int min_speech_time = MIN_SPEECH_DURATION_MS;

        CallConfig.Vad vad = task.getCallConfig().getVad();

        if (vad != null && vad.getMin_silence_time() > 0) {
            min_silence_time = vad.getMin_silence_time();
        }

        if (vad != null && vad.getMin_speech_time() > 0) {
            min_speech_time = vad.getMin_speech_time();
        }

        this.minSilenceSamples = sampleRate * min_silence_time / 1000f; // 8000*1000/1000=16000
        this.minInterruptSpeechSamples = sampleRate * min_speech_time / 1000f; // 200*8000/1000=1400
        this.maxSpeechSamples = sampleRate * MAX_SPEECH_DURATION_MS / 1000f;
        this.roomId = task.getRoomId();
        triggered = false;
        speech_end_pos = 0;
        currentSamples = 0;
        speechBegin = 0;
        speech_segment_duration = 0;
        wakeupCount = 0;
        model.resetStates();

        log.info("vad init complete, roomId {}", task.getRoomId());
        return true;
    }

    @Override
    public void clean() {
        triggered = false;
        speech_end_pos = 0;
        currentSamples = 0;
        speechBegin = 0;
        speech_segment_duration = 0;
        wakeupCount = 0;
        model.resetStates();
    }

    @Override
    public int order() {
        return 5;
    }

    /**
     * currentSamples：
     * tempEnd 含义：检测到说话停止的位置， 即一旦vad阈值小于阈值(说话结束时)时，相对于开始说话时刻累计的采样数。
     *
     * @param data
     */

    public void apply(byte[] data) {
        // Convert the byte array to a float array, 512大小
        float[] audioData = new float[data.length / 2];
        for (int i = 0; i < audioData.length; i++) {
            audioData[i] = ((data[i * 2] & 0xff) | (data[i * 2 + 1] << 8)) / 32767.0f;
        }

        // Get the length of the audio array as the window size
        int windowSizeSamples = audioData.length;

        // Call the model to get the prediction probability of speech
        float speechProb = 0;
        try {
            speechProb = model.call(new float[][]{audioData}, sampleRate)[0];
        } catch (OrtException e) {
            log.error("Error calling model: " + e.getMessage());
            throw new RuntimeException(e);
        }

        // currentSamples：本次说话累计采样数，一直累计，直到说话彻底结束清零
        // speech_end_pos 含义：人声检测低于阈值的位置。

        // 一直累计采样数，直到说话结束重新计数。
        currentSamples += windowSizeSamples;

        // speech_end_pos 不为0（意味着进入额外的等待期），但是人声检测到说话，意味着访客在等待期内重新开始说话，需要清理speech_end_pos的位置重新计算
        if (speechProb >= START_THRESHOLD  && triggered && speech_end_pos != 0) {
            log.info("speak again in the wait time, speechProb: {}, roomId {}, msgId {}, 持续时长 {} 秒", speechProb, roomId, msgId, 1F * (currentSamples - speechBegin) / sampleRate);
            speech_end_pos = 0;
            // 重新计算说话持续时长
            speech_segment_duration = 0;
        }

        // 首次进入人声说话状态，即刚开始说话
        if (speechProb >= START_THRESHOLD && !triggered) {
            speechBegin = currentSamples;
            triggered = true;
            // 开始说话时，初始化打断标识
            msgIssued = false;
            speech_end_pos = 0;
            speech_segment_duration = 0;
            // 开始说话，追问次数清零
            wakeupCount = 0;

            // 生成当前msgId
            msgId = roomId + "_" + System.currentTimeMillis();
            log.info("speak begin, speechProb: {}, roomId {}, msgId {}, perf", speechProb, roomId, msgId);
            contextService.setMsgInfoTrack(msgId, "speak", System.currentTimeMillis());
            Constants.executor.submit(new Runnable() {
                public void run() {
                    // 启动asr会话，耗时大约300ms
                    contextService.getAsrService().startSession(task, msgId);
                }
            });
        }


        // 人声说话状态内，送asr数据
        if (triggered) {
            // 追问，如果访客说话，置空等待标识
            contextService.setAiSpeakEndTime(null);
            try {
                log.trace("speaking...,roomId {}, msgId {}", roomId, msgId);
                // 持续发送给 vad to asr buffer
                contextService.getAsrService().push(msgId, data);
                // 累计说话时长
                speech_segment_duration = speech_segment_duration + windowSizeSamples;
            } catch (Exception e) {
                log.error("fail to push vad buffer", e);
            }
        }

        // 非打断场景
        if (triggered && contextService.isAiSpeechEnd() && !msgIssued) {
            log.info("new_msg_issued, issue new msg, roomId {}, msgId {}", roomId, msgId);
            msgIssued = true;
            //  这个很关键，否则后面的llm和tts都会因为消息Id不一致而被跳过
            contextService.setCurrentMsgId(msgId);
        } else {
            if (triggered) {
                log.debug("debug info, size {}, speech_segment_duration {}, speech_end_pos {}, msgIssued {}", contextService.getTtsToRtcBuffer().size(), speech_segment_duration, speech_end_pos, msgIssued);
            }
            // 访客打断AI场景，不看等待期，说话时长大于阈值
            if (triggered && !contextService.isAiSpeechEnd() && !msgIssued && (speech_segment_duration >= minInterruptSpeechSamples) && speech_end_pos == 0) {
                // 只有超过指定时间的语音才触发打断，且没有进入等待期, 拦截短语音, 区分是Ai说话期间的打断，还是轮到访客开口
                log.info("new_msg_issued_and_interrupt, roomId {}, msgId {}, 持续时长 {} 秒， perf", roomId, msgId, 1F * speech_segment_duration / sampleRate);
                msgIssued = true;
                // 访客说话，打断上一轮回复
                String currentMsgId = contextService.getCurrentMsgId();
                contextService.setMsgInfoTrack(currentMsgId, "interrupt", 0);

                // 执行延时打断
                executor.schedule(() -> {
                    contextService.interrupt();
                }, 500, TimeUnit.MILLISECONDS);

                //  这个很关键，否则后面的llm和tts都会因为消息Id不一致而被跳过
                contextService.setCurrentMsgId(msgId);
            }
        }

        // vad 检测到停止说话 或者 达到最大允许的说话时间
        if ((speechProb < END_THRESHOLD) && triggered) {
            // 开始等待期
            if (speech_end_pos == 0) {
                log.info("speak end by vad, currentSamples {}, speechProb: {}, roomId {}, msgId {}, 持续时长 {} 秒, perf", currentSamples, speechProb, roomId, msgId, 1F * (currentSamples - speechBegin) / sampleRate);
                speech_end_pos = currentSamples;
                contextService.setMsgInfoTrack(msgId, "speak_end", System.currentTimeMillis());
            }

            // 额外的等待期，超出这个等待期意味本轮发言结束
            if (currentSamples - speech_end_pos > minSilenceSamples) {
                // 标记speech 结束
                triggered = false;
                speech_end_pos = 0;
                speech_segment_duration = 0;

                // 如果asr延迟，那就可能取不到完整的asr结果
                String asrRes = contextService.getAsrService().getResult(msgId);
                contextService.setMsgInfoTrack(msgId, "vad_complete_msg", asrRes);


                if (msgIssued) {
                    // 标识为有效消息
                    contextService.getMsgInfoTrack(msgId).setValidMsg(true);
                    // 新消息确认生成，进入访客语音静默模式，防止后续处理环节被AI打断，造成连续打断
                    contextService.setSilence(true, "speak_end", msgId);
                    // 记录有效结束的时间
                    contextService.setMsgInfoTrack(msgId, "speak_complete", System.currentTimeMillis());
                    contextService.setMsgInfoTrack(msgId, "speak_duration", 1000 * (currentSamples - speechBegin) / sampleRate);
                    log.info("speak complete, roomId {}, msgId {}, current asr result: {}, 持续时长 {} 秒, perf", roomId, msgId, asrRes, 1F * (currentSamples - speechBegin) / sampleRate);
                } else {
                    log.info("speak end with silence gap, but msg is not issued, roomId {}, msgId {}, current asr result: {}", roomId, msgId, asrRes);
                }

                msgIssued = false;
                currentSamples = 0;
            }
        }

        Long aiSpeakEndTime = contextService.getAiSpeakEndTime();
        // 处理追问
        if (!triggered && aiSpeakEndTime != null) {
            long l = System.currentTimeMillis() - aiSpeakEndTime;
            if (l >= task.getCallConfig().getVad().getWake_up_time()) {
                if (wakeupCount < task.getCallConfig().getVad().getMax_wake_up_count()) {
                    wakeupCount++;
                    log.info("user speak timeout, trigger wakeup, roomId {}, msgId {}, wakeup time {}, count {}", roomId, msgId, task.getCallConfig().getVad().getWake_up_time(), wakeupCount);
                    contextService.setAiSpeakEndTime(null);
                    executor.schedule(() -> {
                        String res = Utils.calcRandomWeiRes(Constants.EMPTY_ASR_RES);
                        contextService.getTtsService().call(res, roomId, msgId, true);
                        botSubtitleService.sendAIMsg(res, roomId, msgId);
                    }, 500, TimeUnit.MILLISECONDS);
                } else {
                    // 防止重复调用挂机
                    contextService.setAiSpeakEndTime(null);
                    log.info("user speak timeout, user wakeup count over {}, trigger hangUp, roomId {}, msgId {}, wakeup time {}", task.getCallConfig().getVad().getMax_wake_up_count(), roomId, msgId, task.getCallConfig().getVad().getWake_up_time());
                    executor.schedule(() -> {
                        // 挂机，清理
                        contextService.hangupManual(roomId, task.getCallConfig().getChannel(), System.currentTimeMillis());
                    }, 20, TimeUnit.MILLISECONDS);
                }
            }
        }
    }
}
