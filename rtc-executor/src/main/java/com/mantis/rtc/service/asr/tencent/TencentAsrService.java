package com.mantis.rtc.service.asr.tencent;

import com.mantis.micor.pojo.CallConfig;
import com.mantis.micor.pojo.CallTask;
import com.mantis.rtc.buffer.VadToAsrBuffer;
import com.mantis.rtc.service.CallContextService;
import com.mantis.rtc.service.TaskService;
import com.mantis.rtc.service.asr.AsrService;
import com.mantis.rtc.service.asr.volcan.VolcanAsrService;
import com.mantis.rtc.utils.Utils;
import com.tencent.asrv2.AsrConstant;
import com.tencent.asrv2.SpeechRecognizer;
import com.tencent.asrv2.SpeechRecognizerRequest;
import com.tencent.core.ws.Credential;
import com.tencent.core.ws.SpeechClient;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.nio.ByteBuffer;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import static com.mantis.rtc.pojo.Constants.ASR_TTS_EXECUTOR;

@Service
@Slf4j
public class TencentAsrService implements TaskService, AsrService {
    @Autowired
    private CallContextService contextService;

    @Autowired
    private VolcanAsrService volcanAsrService;

    private String roomId = "";

    private CallTask task;

    private CallConfig.TencentAsr tencentAsrConfig;

    private final Map<String, SpeechRecognizer> msgIdWebsocketMap = new ConcurrentHashMap<>();

    /**
     * asr结果， msgId, Asr文本
     */
    private final Map<String, String> msgIdAsrResult = new ConcurrentHashMap<>();

    private final Map<String, VadToAsrBuffer> buffers = new HashMap<>();

    static SpeechClient client = new SpeechClient(AsrConstant.DEFAULT_RT_REQ_URL);


    @Override
    public boolean init(CallTask task) {
        this.task = task;
        this.roomId = task.getRoomId();
        msgIdAsrResult.clear();
        buffers.clear();
        msgIdWebsocketMap.clear();

        CallConfig.ASR asr = task.getCallConfig().getAsr();

        if (!asr.getVendor().equals("tencent")) {
            log.debug("no tencent asr conf, skip init");
            return true;
        }

        tencentAsrConfig = asr.getTencent();
        if (tencentAsrConfig == null || StringUtils.isBlank(tencentAsrConfig.getAppId()) || StringUtils.isBlank(tencentAsrConfig.getSecretKey()) || StringUtils.isBlank(tencentAsrConfig.getSecretId())) {
            log.error("invalid tencent asr config, conf {}", asr);
            return false;
        }

        log.info("tencent asr init complete, roomId {}", task.getRoomId());
        return true;
    }


    @Override
    public void clean() {
        msgIdAsrResult.clear();
        buffers.clear();
        Collection<SpeechRecognizer> speechRecognizers = msgIdWebsocketMap.values();
        for (SpeechRecognizer speechRecognizer : speechRecognizers) {
            speechRecognizer.close();
        }
        msgIdWebsocketMap.clear();
    }

    public void setResult(String msgId, String result) {
        if (StringUtils.isNotBlank(result)) {
            msgIdAsrResult.put(msgId, result);
        }
    }

    @Override
    public int order() {
        return 0;
    }

    @Override
    public boolean startSession(CallTask task, String msgId) {
        if(!contextService.isTelChanel(task) && contextService.isCheckGender()) {
            // 启动火山的识别
            ASR_TTS_EXECUTOR.submit(new Runnable() {
                public void run() {
                    volcanAsrService.startSession(task, msgId);
                }
            });
        }

        Credential credential = new Credential(tencentAsrConfig.getAppId(), tencentAsrConfig.getSecretId(), tencentAsrConfig.getSecretKey());
        SpeechRecognizerRequest request = SpeechRecognizerRequest.init();

        int sampleRate = task.getSampleRate();
        if (sampleRate == 8000) {
            request.setEngineModelType("8k_zh_large");
        } else {
            request.setEngineModelType("16k_zh_large");
        }
        // PCM:1
        request.setVoiceFormat(1);
        request.setVoiceId(msgId);//voice_id为请求标识，需要保持全局唯一，遇到问题需要提供该值方便服务端排查
        log.info("connect tencent asr, roomId {}, voice_id:{}", roomId, request.getVoiceId());

        MantisSpeechRecognizerListener listener = new MantisSpeechRecognizerListener(contextService, this, roomId);
        SpeechRecognizer speechRecognizer = null;
        try {
            long currentTimeMillis = System.currentTimeMillis();
            speechRecognizer = new SpeechRecognizer(client, credential, request, listener);
            speechRecognizer.start();
            log.info("speechRecognizer start latency {}", (System.currentTimeMillis() - currentTimeMillis) + " ms");
            onWebsocketConnected(speechRecognizer, task.getRoomId(), msgId);
        } catch (Exception e) {
            log.error("tencent asr, fail to start session, roomId {}, msgId {}", task.getRoomId(), msgId, e);
            return false;
        }
        return true;
    }


    /**
     * 连接完成的处理：
     * 1. 建立msgId与SpeechRecognizer的映射
     * 2. 针对本轮消息，启动一个线程，用于数据推送
     *
     * @param speechRecognizer
     * @param msgId
     */
    private void onWebsocketConnected(SpeechRecognizer speechRecognizer, String roomId, String msgId) {
        msgIdWebsocketMap.put(msgId, speechRecognizer);
        // 启动推流线程
        Thread pushThread = new Thread(new TencentPushToAsrTask(speechRecognizer, roomId, msgId));
        pushThread.start();
    }

    @Override
    public void push(String msgId, byte[] data) {
        if (buffers.get(msgId) == null) {
            buffers.put(msgId, new VadToAsrBuffer());
        }
        buffers.get(msgId).enqueue(data);

        if (!contextService.isTelChanel(task) && contextService.isCheckGender()) {
            // 保存到性别识别
            volcanAsrService.push(msgId, data);
        }
    }


    @Override
    public String getResult(String msgId) {
        return msgIdAsrResult.get(msgId);
    }

    public void removeSpeechRecognizer(String msgId) {
        msgIdWebsocketMap.remove(msgId);
    }


    /**
     * 对于每个msg，都创建一个新线程来进行数据发送
     * 处理数据发送与停止
     * 建议每40ms 发送40ms 时长
     */
    class TencentPushToAsrTask implements Runnable {
        private final String msgId;
        private final String roomId;
        private final SpeechRecognizer speechRecognizer;
        private boolean hasSent = false;

        public TencentPushToAsrTask(SpeechRecognizer speechRecognizer, String roomId, String msgId) {
            this.speechRecognizer = speechRecognizer;
            this.roomId = roomId;
            this.msgId = msgId;
        }

        private void onAsrDataSentComplete(String msgId) {
            log.info("tencent asr, onAsrDataSentComplete, roomId {}, msgId {}", roomId, msgId);
            contextService.setMsgInfoTrack(msgId, "send_asr_data_complete", System.currentTimeMillis());
            // 数据发送完毕，发送停止指令
            try {
                speechRecognizer.stop();
            } catch (Exception e) {
                log.error("fail to stop tencent asr, roomId {}, msgId {}", roomId, msgId, e);
            }
        }

        private void doAsr(CallTask task, String msgId, ByteBuffer buffer, boolean isLast) {
            byte[] array = buffer.array();
            speechRecognizer.write(array);
        }

        @Override
        public void run() {
            // 40ms 一个批次
            int batch_duration = 40;
            int batch_size = 2 * task.getSampleRate() * batch_duration / 1000;

            while (true) {
                if (!contextService.isWorking()) {
                    log.info("TencentPushToAsrTask quit, roomId {}, msgId {}", roomId, msgId);
                    break;
                }

                VadToAsrBuffer vadToAsrBuffer = buffers.get(msgId);
                if (vadToAsrBuffer == null) {
                    log.info("TencentPushToAsrTask, vadToAsrBuffer is null, roomId {}, msgId {}", roomId, msgId);
                    Utils.sleep(batch_duration);
                    continue;
                }

                byte[] dequeue = vadToAsrBuffer.dequeue(batch_size, true);
                if (dequeue == null || dequeue.length == 0) {
                    if (!hasSent) {
                        Utils.sleep(batch_duration);
                    } else {
                        onAsrDataSentComplete(msgId);
                        break;
                    }
                } else {
                    log.trace("TencentPushToAsrTask, roomId {}, msgId {}, length {}", task.getRoomId(), msgId, dequeue.length);
                    hasSent = true;
                    doAsr(task, msgId, ByteBuffer.wrap(dequeue), false);
                    Utils.sleep(batch_duration);
                }
            }
        }
    }
}
