package com.mantis.rtc.service.tts.cache.cosyvoice;

import com.alibaba.dashscope.audio.ttsv2.SpeechSynthesisAudioFormat;
import com.alibaba.dashscope.audio.ttsv2.SpeechSynthesisParam;
import com.alibaba.dashscope.audio.ttsv2.SpeechSynthesizer;
import com.mantis.micor.pojo.CallConfig;
import com.mantis.micor.pojo.CallTask;
import com.mantis.rtc.pojo.Constants;
import com.mantis.rtc.service.tts.cache.TtsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.nio.ByteBuffer;
import java.util.Base64;

import static com.mantis.rtc.service.rtc.utils.RtcConstants.SAMPLE_RATE_8000;

/**
 * CosyVoiceService 的问题，在于api不能透传msgId参数，因此要实现打断效果会比较麻烦。只能每个消息创建连接，这样耗时。。。
 * 没有session的概念，如果有，可以用msgId作为sessionId
 * 可以通过连接池的方式来
 */
@Service
@Slf4j
public class CosyVoiceService implements TtsService {

    @Override
    public boolean accept(CallTask task) {
        return task.getCallConfig().getTts().getVendor().equalsIgnoreCase(Constants.TTS_VENDOR_COSYVOICE);
    }

    @Override
    public String tts(String query, CallTask task) {
        CallConfig.CosyvoiceTTS cosyVoice = task.getCallConfig().getTts().getCosyvoice();
        SpeechSynthesisParam param = null;
        if (task.getSampleRate() == SAMPLE_RATE_8000) {
            param = SpeechSynthesisParam.builder()
                    .apiKey(cosyVoice.getAppkey())
                    .model("cosyvoice-v2")
                    .voice(cosyVoice.getVoiceId())
                    .format(SpeechSynthesisAudioFormat.PCM_8000HZ_MONO_16BIT) // 流式合成使用PCM
                    .build();
        } else {
            param = SpeechSynthesisParam.builder()
                    .apiKey(cosyVoice.getAppkey())
                    .model("cosyvoice-v2")
                    .voice(cosyVoice.getVoiceId())
                    .format(SpeechSynthesisAudioFormat.PCM_16000HZ_MONO_16BIT) // 流式合成使用PCM
                    .build();
        }

        SpeechSynthesizer synthesizer = new SpeechSynthesizer(param, null);
        ByteBuffer buffer = synthesizer.call(query);
        if (buffer != null) {
            return Base64.getEncoder().encodeToString(buffer.array());
        }
        return null;
    }
}
