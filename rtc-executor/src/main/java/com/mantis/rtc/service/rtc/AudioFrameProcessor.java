package com.mantis.rtc.service.rtc;


import com.mantis.rtc.pojo.Constants;
import com.mantis.rtc.service.CallContextService;
import com.mantis.rtc.service.rtc.utils.RtcConstants;
import com.ss.bytertc.engine.data.RemoteStreamKey;
import com.ss.bytertc.engine.handler.IAudioFrameProcessor;
import com.ss.bytertc.engine.utils.IAudioFrame;
import lombok.extern.slf4j.Slf4j;

import java.nio.ByteBuffer;


@Slf4j
public class AudioFrameProcessor implements IAudioFrameProcessor {

    private CallContextService contextService = null;


    public AudioFrameProcessor(CallContextService contextService) {
        this.contextService = contextService;
    }

    /**
     * 每10毫秒收到一帧音频数据
     * 会在一个单独的线程中执行，但只有一个线程
     *
     * @param stream_info 流信息
     * @param audioFrame  声音数据
     * @return
     */
    @Override
    public int onProcessRemoteUserAudioFrame(RemoteStreamKey stream_info, IAudioFrame audioFrame) {
        if (audioFrame == null) {
            log.error("invalid audioFrame");
            return 0;
        }
        ByteBuffer dataBuffer = audioFrame.getDataBuffer();
        if (dataBuffer == null || !dataBuffer.hasRemaining()) {
            log.error("invalid data buffer, roomId {}, uid {}, sample_rate {}, channels {}", stream_info.roomId, stream_info.uid, audioFrame.sample_rate().value, audioFrame.channel().value);
            return 0;
        }

        // 获取音频数据
        int length = audioFrame.data_size();
        byte[] data = new byte[length];
        dataBuffer.get(data);

        // 录制
        contextService.getRecordService().recordUser(data);

        // 不允许打断， 忽略语音
        if(!contextService.isAllowInterrupted()) {
            log.trace("call is not allow interrupt, skip new message, roomId {}", stream_info.roomId);
            return 0;
        }

        // 欢迎语正在播放，忽略语音
        if(!contextService.isWelcomeComplete()) {
            log.debug("welcome is not complete, skip new message, roomId {}", stream_info.roomId);
            return 0;
        }
        // 启动挂机程序后，不在接收语音
        if(contextService.isHookStatus()){
            log.debug("hook skip new message, roomId {}", stream_info.roomId);
            return 0;
        }

        // 静默期内，不接受新的语音数据
        if(contextService.isSilence()) {
            log.trace("in silence status, skip new message, roomId {}", stream_info.roomId);
            return 0;
        }

        Float volumeGain = contextService.getTask().getCallConfig().getRtc().getVolumeGain();
        if(volumeGain != null && volumeGain > 0 && volumeGain < 1) {
            Constants.adjustVolume(data, length, volumeGain);
        }

        contextService.getRtcToVadBuffer().enqueue(data);

        // 执行VAD 检测
        while (contextService.getRtcToVadBuffer().size() >= RtcConstants.VAD_16000_BATCH_SAMPLE_COUNT) {
            // 如果任务结束，退出
            if(!contextService.isWorking()) {
                log.info("rtc2Vad task exit, roomId {}", stream_info.roomId);
                break;
            }
            // 获取VAD数据
            byte[] dequeue = contextService.getRtcToVadBuffer().dequeue(RtcConstants.VAD_16000_BATCH_SAMPLE_COUNT);
            if(dequeue == null || dequeue.length == 0) {
                continue;
            }
            // 执行vad检测
            contextService.getVadService().apply(dequeue);
        }

        return 0;
    }




    @Override
    public int onProcessRecordAudioFrame(IAudioFrame audioFrame) {
        return 0;
    }

    @Override
    public int onProcessPlayBackAudioFrame(IAudioFrame audioFrame) {
        return 0;
    }

    @Override
    public int onProcessScreenAudioFrame(IAudioFrame audioFrame) {
        return 0;
    }

}

