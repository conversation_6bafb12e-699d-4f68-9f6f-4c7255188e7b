package com.mantis.rtc.service.llm;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.mantis.rtc.pojo.chat.VolcSearchKnowledgeRespDTO;
import com.volcengine.auth.ISignerV4;
import com.volcengine.auth.impl.SignerV4Impl;
import com.volcengine.model.Credentials;
import com.volcengine.service.SignableRequest;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpResponse;
import org.apache.http.NameValuePair;
import org.apache.http.client.HttpClient;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.utils.URIBuilder;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class VolcSign {
  public static SignableRequest prepareRequest(String host, String path, String method, List<NameValuePair> params, String body, String ak, String sk) throws Exception {
    SignableRequest request = new SignableRequest();
    request.setMethod(method);
    request.setHeader("Accept", "application/json");
    request.setHeader("Content-Type", "application/json");
    request.setHeader("Host", "api-knowledgebase.mlp.cn-beijing.volces.com");
    request.setEntity(new StringEntity(body, "utf-8"));

    URIBuilder builder = request.getUriBuilder();
    builder.setScheme("https");
    builder.setHost(host);
    builder.setPath(path);
    if (params != null) {
      builder.setParameters(params);
    }

    RequestConfig requestConfig = RequestConfig.custom().setSocketTimeout(5000).setConnectTimeout(2000).build();
    request.setConfig(requestConfig);

    Credentials credentials = new Credentials("cn-north-1", "air");
    credentials.setAccessKeyID(ak);
    credentials.setSecretAccessKey(sk);

    // 签名
    ISignerV4 ISigner = new SignerV4Impl();
    ISigner.sign(request, credentials);

    return request;
  }

  private static String VOLCE_AK = "AKLTODRkZTY2YjNjOTQ0NDBiMmI0Y2Y3N2ViZjczYTMxZGE";
  private static String VOLCE_SK = "TlRKak1UZGlNek0xTldFME5HUXpZamd6TWpKaFpHUmlZVFptWmpsalpUSQ==";
  private static String resourceId = "your_resource_id";
//  private static String name = "health_class";
  private static String name = "haichen_test";
  private static String query = "今天没空看了";
  public static void main(String[] args) throws Exception {
    String host = "api-knowledgebase.mlp.cn-beijing.volces.com";
    String path = "/api/knowledge/collection/search_knowledge";
    String method = "POST";

    Map<String, Object> reqMap = new HashMap<>();
//    reqMap.put("resource_id", resourceId);//知识库唯一 id
    reqMap.put("name", name);//知识库名

//    Map<String, Object> doc_filter = new HashMap<>();
//    doc_filter.put("op", "must");
//    doc_filter.put("field", "公司");
//    List<Integer> companyIdList = new ArrayList<>();
//    companyIdList.add(50035);
//    doc_filter.put("conds", companyIdList);
//    Map<String, Object> query_param_map = new HashMap<>();
//    query_param_map.put("doc_filter", doc_filter);
//    reqMap.put("query_param", query_param_map);

    if(query.length() > 8000){
      query = query.substring(0,8000);
    }
    reqMap.put("query", query);//查询条件
    reqMap.put("limit", 2);//查询条件

    String body = JSON.toJSONString(reqMap);
    SignableRequest request = prepareRequest(host, path, method, null, body, VOLCE_AK, VOLCE_SK);
    System.out.println(request.getURI());
    System.out.println("-----------------------");
    HttpClient client = HttpClients.createDefault();
    HttpResponse response = client.execute(request);

    int statusCode = response.getStatusLine().getStatusCode();
    String content = EntityUtils.toString(response.getEntity());
//    System.out.println(content);
    if(StringUtils.isNotBlank(content)){
      VolcSearchKnowledgeRespDTO volcSearchKnowledgeRespDTO = JSONObject.parseObject(content, VolcSearchKnowledgeRespDTO.class);
      if(null != volcSearchKnowledgeRespDTO && Integer.valueOf(0).equals(volcSearchKnowledgeRespDTO.getCode())){
        if(null != volcSearchKnowledgeRespDTO.getData() && null != volcSearchKnowledgeRespDTO.getData().getResult_list() && volcSearchKnowledgeRespDTO.getData().getResult_list().size() > 0){
          for (VolcSearchKnowledgeRespDTO.VolcSearchKnowledge v : volcSearchKnowledgeRespDTO.getData().getResult_list()){
            System.out.println(JSON.toJSONString(v));
          }
//          System.out.println("1111111");
        }else{
          System.out.println("返回空："+volcSearchKnowledgeRespDTO.getRequest_id());
        }
      }else{
        System.out.println("请求失败："+volcSearchKnowledgeRespDTO.getMessage());
      }
    }else {
      System.out.println("请求失败返回空："+statusCode);
    }

    System.out.println("-----------------------");
//    System.out.println(statusCode);
//    System.out.println(content);
  }
}