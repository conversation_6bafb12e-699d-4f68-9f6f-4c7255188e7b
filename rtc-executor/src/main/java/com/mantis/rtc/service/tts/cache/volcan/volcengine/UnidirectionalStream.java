package com.mantis.rtc.service.tts.cache.volcan.volcengine;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mantis.micor.pojo.CallConfig;
import com.mantis.micor.pojo.CallTask;
import com.mantis.rtc.pojo.Constants;
import com.mantis.rtc.service.tts.cache.TtsService;
import com.mantis.rtc.service.tts.cache.volcan.protocol.EventType;
import com.mantis.rtc.service.tts.cache.volcan.protocol.Message;
import com.mantis.rtc.service.tts.cache.volcan.protocol.MsgType;
import com.mantis.rtc.service.tts.cache.volcan.protocol.SpeechWebSocketClient;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.net.URI;
import java.net.URISyntaxException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Map;
import java.util.UUID;

@Service
@Slf4j
public class UnidirectionalStream implements TtsService {
    private static final String ENDPOINT = "wss://openspeech.bytedance.com/api/v3/tts/unidirectional/stream";
    private static final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public boolean accept(CallTask task) {
        return task.getCallConfig().getTts().getVendor().equalsIgnoreCase(Constants.TTS_VENDOR_VOLCAN);
    }

    @Override
    public String tts(String query, CallTask task) {
        CallConfig.VolcanTTS volcan = task.getCallConfig().getTts().getVolcan();
        String roomId = task.getRoomId();

        String voiceId = volcan.getVoiceId();
        String cluster = null;
        if (voiceId.startsWith("S_")) {
            cluster = "volc.megatts.default";
        } else {
            cluster = "volc.service_type.10029";
        }

        Map<String, String> headers = Map.of(
                "X-Api-App-Key", volcan.getAppId(),
                "X-Api-Access-Key", volcan.getAppKey(),
                "X-Api-Resource-Id", cluster,
                "X-Api-Connect-Id", UUID.randomUUID().toString());


        // Create WebSocket client
        SpeechWebSocketClient client = null;
        try {
            client = new SpeechWebSocketClient(new URI(ENDPOINT), headers, roomId);
        } catch (URISyntaxException e) {
            log.error("fail to init volcan tts service, roomId {}", roomId, e);
            return null;
        }

        try {
            client.connectBlocking();
        } catch (InterruptedException e) {
            log.error("fail to connect volcan tts service, roomId {}", roomId, e);
            return null;
        }

        // Prepare request parameters
        Map<String, Object> request = null;
        try {
            request = Map.of(
                    "user", Map.of("uid", UUID.randomUUID().toString()),
                    "req_params", Map.of(
                            "speaker", voiceId,
                            "model", "seed-tts-1.1",
                            "audio_params", Map.of(
                                    "format", "pcm",
                                    "sample_rate", task.getSampleRate(),
                                    "loudness_rate", volcan.getLoudness() == null ? -50 : volcan.getLoudness(),
                                    "speech_rate", volcan.getSpeed() == null ? 0 : volcan.getSpeed(),
                                    "enable_timestamp", true),
                            // additions requires a JSON string
                            "additions", objectMapper.writeValueAsString(Map.of(
                                    "max_length_to_filter_parenthesis", 100,
                                    "enable_latex_tn", false,
                                    "disable_emoji_filter", true,
                                    "disable_markdown_filter", false)),
                            "text", query));

            // Send request
            client.sendFullClientMessage(objectMapper.writeValueAsBytes(request));

            // Receive response
            ByteArrayOutputStream audioStream = new ByteArrayOutputStream();
            while (true) {
                Message msg = client.receiveMessage();
                if (msg.getType() == MsgType.AUDIO_ONLY_SERVER) {
                    if (msg.getPayload() != null) {
                        audioStream.write(msg.getPayload());
                    }
                } else if (msg.getType() == MsgType.ERROR) {
                    throw new RuntimeException("Server returned error: " + new String(msg.getPayload()));
                }

                if (msg.getType() == MsgType.FULL_SERVER_RESPONSE &&
                    msg.getEvent() == EventType.TTS_SENTENCE_END) {
                    String jsonString = new String(msg.getPayload(), StandardCharsets.UTF_8);
                    log.debug("Received TTS response sentence end: {}, roomId {}", jsonString, roomId);
                    continue;
                }

                if (msg.getType() == MsgType.FULL_SERVER_RESPONSE &&
                    msg.getEvent() == EventType.SESSION_FINISHED) {
                    break;
                }
            }

            if (audioStream.size() == 0) {
                log.error("audio stream is empty, roomId {}", roomId);
                return null;
            }

            return Base64.getEncoder().encodeToString(audioStream.toByteArray());

        } catch (Exception e) {
            log.error("fail to send tts message, roomId {}", roomId, e);
        } finally {
            try {
                client.closeBlocking();
            } catch (InterruptedException e) {
                log.error("fail to close volcan tts websocket, roomId {}", roomId, e);
            }
        }
        return null;
    }
}