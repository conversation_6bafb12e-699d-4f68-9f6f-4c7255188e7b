package com.mantis.rtc.service.tts.volcan.stream;

import com.google.gson.JsonObject;
import com.mantis.micor.pojo.CallConfig;
import com.mantis.micor.pojo.CallTask;
import com.mantis.rtc.pojo.Constants;
import com.mantis.rtc.service.CallContextService;
import com.mantis.rtc.service.TaskService;
import com.mantis.rtc.service.tts.cache.TTSCacheService;
import com.mantis.rtc.service.tts.TTSStreamService;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import okio.ByteString;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.util.Map;
import java.util.Queue;
import java.util.UUID;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * https://www.volcengine.com/docs/6561/1329505
 * 启动时，建立连接，避免任务初始化时创建连接的耗时
 * 针对每条消息，开启session
 */
@Service
@Slf4j
public class VolcanTTSStreamService implements TaskService, TTSStreamService {

    private final Lock lock = new ReentrantLock();

    @Autowired
    @Qualifier("volTTSWebsocketClient")
    private OkHttpClient okHttpClient;

    @Autowired
    private CallContextService contextService;

    @Autowired
    private TTSCacheService ttsCacheService;

    /**
     * websocket 连接池，用于缓存连接
     */
    private final Queue<WebSocket> queue = new ArrayBlockingQueue<>(10) ;

    /**
     * msgId与对应使用的websocket映射
     * 保存是都是空闲的链接
     */
    private final Map<String, WebSocket> webSocketMap = new ConcurrentHashMap<>();
    private CallTask task = null;
    private String roomId = "";

    private CallConfig.VolcanTTS tts = null;

    private static final int MAX_POOL_SIZE = 3;

    /**
     * 驱动tts，一次性发送tts
     * @param sentence
     * @param roomId
     * @param msgId
     */
    @Override
    public void call(String sentence, String roomId, String msgId, boolean complete) {
        if(StringUtils.isBlank(sentence)) {
            // 停止静默
            contextService.setSilence(false, "empty query in tts", msgId);
            return;
        }

        // 直接从内存中获取tts数据
        byte[] bytes = ttsCacheService.getCache().get(sentence);

        if(bytes != null) {
            if(StringUtils.isNotBlank(msgId) && msgId.contains("welcome")) {
                log.info("got tts data by cache, roomId {}, msgId {}, msg {}", roomId, msgId, sentence);
                contextService.getWelcomeBuffer().enqueue(bytes);
                if(complete) {
                    contextService.setWelcomeSent(true);
                }
            } else {
                log.info("got tts data by cache msg {}, roomId {}, msgId {}", sentence, roomId, msgId);
                // 存入缓存
                contextService.getTtsToRtcBuffer().enqueue(bytes, msgId);
                // 停止静默
                contextService.setSilence(false, "complete tts by cache", msgId);
            }
            return;
        }

        // 如果未能从缓存获取，调用流式接口获取
        log.info("fail to get audio from cache, call tts stream service, send text {} to tts, roomId {}, msgId {}", sentence, roomId, msgId);

        WebSocket webSocket = fetchWebsocketFromQueue(msgId);
        if(webSocket == null){
            log.error("tts websocket is null, roomId {}, skip", roomId);
            // 停止静默
            contextService.setSilence(false, "no websocket in tts", msgId);
            return;
        }

        try{
            startSession(roomId, msgId);
            //发送数据
            sendTTSMessage(webSocket, msgId, sentence);
        }finally {
            completeSession(msgId);
        }
    }

    /**
     * 大模型返回开始标识时调用，建立session
     * @param roomId
     * @param msgId
     */
    @Override
    public void onChatStreamStart(String roomId, String msgId){
        log.info("tts sent stream start, roomId {}, msgId {}, perf", roomId, msgId);
        WebSocket webSocket = fetchWebsocketFromQueue(msgId);
        if(webSocket == null){
            log.error("tts websocket is null, roomId {}, skip", roomId);
            return;
        }
        startSession(msgId, msgId);
    }

    /**
     * 大模型流式回复时调用
     * @param sentence
     * @param msgId
     */
    @Override
    public void streamingCall(String sentence, String msgId) {
        if(StringUtils.isBlank(sentence)) {
            return;
        }
        // 打断模式下，不再送文本消息到tts
        if(contextService.isInterrupted(msgId)) {
            log.info("tts streamingCall skip, because interrupted, roomId {}", roomId);
            return;
        }

        if(!contextService.isCurrentMsgId(msgId)) {
            log.info("tts streamingCall skip, because isCurrentMsgId, roomId {}, msgId {}, currentMsgId {}", roomId, msgId, contextService.getCurrentMsgId());
            return;
        }

        lock.lock();
        try {
            WebSocket webSocket = webSocketMap.get(msgId);
            if(webSocket == null) {
                log.error("streamingCall, fail to fetch websocket by msgId {}", msgId);
                return;
            }
            log.debug("streamingCall, roomId {}, msgId {}, sentence: {}, thread {}", roomId, msgId, sentence, Thread.currentThread().getName());
            sendTTSMessage(webSocket, msgId, sentence);
        } finally {
            lock.unlock();
        }
    }

    @Override
    public void completeSession(String msgId) {
        log.debug("try to completeSession, roomId {}, msgId {}", roomId, msgId);
        WebSocket webSocket = webSocketMap.remove(msgId);
        if(webSocket == null) {
            log.debug("fail to got websocket by msgId {}", msgId);
            return;
        }
        // 标识对话完成
        finishSession(webSocket, msgId);
    }

    /**
     * 开始session
     * @param roomId
     * @param msgId
     */

    private void startSession(String roomId, String msgId) {
        log.info("tts startSession, roomId {}, msgId {}", roomId, msgId);
        WebSocket webSocket = webSocketMap.get(msgId);
        if(webSocket == null) {
            log.error("fail to got tts websocket when startSession, roomId {}, msgId {}", roomId, msgId);
            return;
        }
        startTTSSession(webSocket, msgId);
    }

    /**
     * 初始化任务时，与tts建立连接
     * @param task
     * @return
     */
    @Override
    public boolean init(CallTask task) {
        this.task = task;
        this.roomId = task.getRoomId();

        String vendor = task.getCallConfig().getTts().getVendor();
        if(!vendor.equalsIgnoreCase(Constants.TTS_VENDOR_VOLCAN)) {
            log.info("no volcan tts conf, skip init, roomId {}", task.getRoomId());
            return true;
        }

        this.tts = task.getCallConfig().getTts().getVolcan();
        if (tts == null) {
            log.error("tts config is invalid, roomId {}", roomId);
            return false;
        }

        if (StringUtils.isBlank(tts.getAppId())) {
            log.error("tts appId is invalid, roomId {}", roomId);
            return false;
        }

        if (StringUtils.isBlank(tts.getAppKey())) {
            log.error("tts token is invalid, roomId {}", roomId);
            return false;
        }

        if (StringUtils.isBlank(tts.getVoiceId())) {
            log.error("tts voiceId is invalid, roomId {}", roomId);
            return false;
        }
        log.info("begin init volcan tts stream service, roomId {}", task.getRoomId());
        initWebsocketPool(roomId);
        return true;
    }

    private void initWebsocketPool(String roomId){
        for (int i = 0; i < MAX_POOL_SIZE - queue.size() ; i++) {
            Constants.ASR_TTS_EXECUTOR.submit(new Runnable() {
                @Override
                public void run() {
                    connectTts(roomId);
                }
            });
        }
    }

    /**
     * 从queue中获取websocket，如果没有的话，则调用initWebsocketPool 进行初始化，尝试4次，每次间隔10ms
     * @return
     */
    private WebSocket fetchWebsocketFromQueue(String msgId) {
        if(webSocketMap.containsKey(msgId)) {
            return webSocketMap.get(msgId);
        }

        for (int i = 0; i < 4; i++) {
            WebSocket webSocket = queue.poll();
            if (webSocket != null) {
                webSocketMap.put(msgId, webSocket);
                log.info("fetch tts websocket success, msgId {}, roomId {}, left {}", msgId, task.getRoomId(), queue.size());
                // 每次调用时，都尝试维持连接池的数量
                initWebsocketPool(roomId);
                return webSocket;
            }

            log.info("fail to get tts websocket, msgId {}, will retry {}, roomId {}", msgId, i + 1, task.getRoomId());
            try {
                Thread.sleep(10);
            } catch (InterruptedException e) {
                log.error("fail to sleep", e);
            }
        }

        log.error("fail to get tts websocket and retry, msgId {},  roomId {}", msgId, task.getRoomId());
        return null;
    }


    @Override
    public void clean() {
        webSocketMap.clear();
        while (true) {
            WebSocket client = queue.poll();
            if (client == null) {
                break;
            }
            try {
                VolcanTTSUtils.finishConnection(client);
                client.cancel();
//                client.close(1000, "clean");
            } catch (Exception e) {
                log.error("fail to clean tts websocket", e);
            }
        }
    }

    private void connectTts(String roomId) {
        String streamCluster = null;

        String voiceId = tts.getVoiceId();
        // 默认是声音复刻 volc.megatts.default, 火山模型：volc.service_type.10029
        if(voiceId.startsWith("zh_") || voiceId.startsWith("ICL_zh_")) {
            streamCluster = "volc.service_type.10029";
        } else {
            streamCluster = "volc.megatts.default";
        }

        log.debug("beigin connect volcan tts, roomId {}", roomId);
        final Request request = new Request.Builder()
                .url("wss://openspeech.bytedance.com/api/v3/tts/bidirection")
                .header("X-Api-App-Key", tts.getAppId())
                .header("X-Api-Access-Key", tts.getAppKey())
                .header("X-Api-Resource-Id", streamCluster)
                .header("X-Api-Connect-Id", UUID.randomUUID().toString())
                .build();

        okHttpClient.newWebSocket(request, new WebSocketListener() {
            private boolean isSessionFirstReply = true;
            // 注意：线程不安全
            @Override
            public void onOpen(WebSocket webSocket, Response response) {
                VolcanTTSUtils.startConnection(webSocket);
                queue.add(webSocket);
                log.info("volcan tts websocket open, logId {}, roomId {}, queue size {}", response.header("X-Tt-Logid"), roomId, queue.size());
            }

            @Override
            public void onMessage(WebSocket webSocket, ByteString bytes) {
                // 如果挂机，直接返回
                if(!contextService.isWorking()) {
                    return;
                }
                // 解析事件
                VolcanTTSUtils.TTSResponse response = VolcanTTSUtils.parserResponse(bytes.toByteArray());
                log.debug("===>tts response: {}", response);

                switch (response.optional.event) {
                    case VolcanTTSUtils.EVENT_ConnectionFinished:
                        log.debug("EVENT_ConnectionFinished, roomId {}, connectionId {}", roomId, response.optional.connectionId);
                        // 连接关闭时，断开连接
                        webSocket.close(1000, "finish");
                        break;
                    case VolcanTTSUtils.EVENT_ConnectionFailed:
                        log.error("EVENT_ConnectionFailed, roomId {}, connectionId {}", roomId, response.optional.connectionId);
                        break;
                    case VolcanTTSUtils.EVENT_SessionStarted:
                        log.debug("EVENT_SessionStarted, msgId {}", response.optional.sessionId);
                        isSessionFirstReply = true;
                        break;
                    case VolcanTTSUtils.EVENT_SessionFailed:
                        log.error("EVENT_SessionFailed, msgId {}", response.optional.sessionId);
                        VolcanTTSUtils.finishConnection(webSocket);
                        break;
                    case VolcanTTSUtils.EVENT_SessionFinished:
                        log.debug("EVENT_SessionFinished, sessionId {}", response.optional.sessionId);
                        // session结束，请求关闭连接
                        VolcanTTSUtils.finishConnection(webSocket);
                        break;
                    case VolcanTTSUtils.EVENT_TTSResponse:
                        if (response.payload == null) {
                            return;
                        }

                        // 输出结果
                        if (response.header.message_type == VolcanTTSUtils.AUDIO_ONLY_RESPONSE) {

                            String msgId = response.optional.sessionId;
                            if(StringUtils.isNotBlank(msgId) && msgId.contains("welcome")) {
                                log.debug("on tts data of welcome, roomId {}, msgId {}, size {}", roomId, msgId, response.payloadSize);
                                contextService.getWelcomeBuffer().enqueue(response.payload);
                                contextService.setWelcomeSent(true);
                            } else {
                                if(isSessionFirstReply) {
                                    log.info("on first tts data, roomId {}, msgId {}, size {}, perf", roomId, msgId, response.payloadSize);
                                    isSessionFirstReply = false;
                                    contextService.setMsgInfoTrack(msgId, "tts_first_token", System.currentTimeMillis());
                                    //AI 开始说话了，需要退出用户静默阶段，允许打断
                                    contextService.setSilence(false, "tts_stream_begin", msgId);
                                }
                                // 如果当前消息没有被打断，则写入rtc buffer
                                if(contextService.isCurrentMsgId(msgId)) {
                                    log.debug("on tts data, roomId {}, msgId {}, size {}", roomId, msgId, response.payloadSize);
                                    // 存入缓存
                                    contextService.getTtsToRtcBuffer().enqueue(response.payload, msgId);
                                } 
                                
                                // 如果当前消息被打断，则请求关闭连接
                                if(contextService.isInterrupted(msgId)) {
                                    log.info("on tts data is interrupted, roomId {}, msgId {}", roomId, msgId);
                                    // 打断， 请求关闭连接
                                    VolcanTTSUtils.finishConnection(webSocket);
                                }

                            }
                        } else if (response.header.message_type == VolcanTTSUtils.FULL_SERVER_RESPONSE) {
//                            log.debug("FULL_SERVER_RESPONSE {}, roomId {}, msgId {}" ,new String(response.payload), roomId, response.optional.sessionId);
                        }
                        break;
                    default:
                        log.debug("===>response default {}, roomId {}" , response.optional.event, roomId);
                        break;
                }
            }

            @Override
            public void onMessage(WebSocket webSocket, String text) {
                log.info("volcan tts websocket onMessage of text, roomId {}, text {}" , roomId, text);
            }

            public void onClosing(WebSocket webSocket, int code, String reason) {
//                log.debug("volcan tts websocket onClosing, roomId {}" , roomId);
            }

            @Override
            public void onClosed(WebSocket webSocket, int code, String reason) {
                log.debug("volcan tts websocket closed, roomId {}, reason {}" , roomId, reason);
                queue.remove(webSocket); // 显式移除失效连接
                webSocketMap.values().removeIf(ws -> ws == webSocket);
            }

            @Override
            public void onFailure(WebSocket webSocket, Throwable t, Response response) {
                log.info("volcan tts websocket onFailure, roomId {}, Throwable {}, Response {}" , roomId, t.getMessage(), " Response:" + (response == null ? "null" : response.toString()));
                queue.remove(webSocket); // 显式移除失效连接
                webSocketMap.values().removeIf(ws -> ws == webSocket);
            }
        });
    }

    /**
     * 发送整段文字合成
     * @param webSocket
     * @param sessionId
     * @param text
     * @return
     */
    private boolean sendTTSMessage(WebSocket webSocket, String sessionId, String text) {
        byte[] payload = calcPayload(VolcanTTSUtils.EVENT_TaskRequest, text);
        return VolcanTTSUtils.sendMessage(webSocket, sessionId, payload);
    }


    /**
     * 针对某个msgId，开始会话
     * @param webSocket
     * @param msgId
     * @return
     */
    private boolean startTTSSession(WebSocket webSocket, String msgId) {
        log.debug("start tts session, roomId {}, msgId {}", roomId, msgId);
        byte[] payload = calcPayload(VolcanTTSUtils.EVENT_StartSession, "");
        return VolcanTTSUtils.startSession(webSocket, msgId, payload);
    }

    private byte[] calcPayload(int event, String text) {
        JsonObject payloadJObj = new JsonObject();

        JsonObject user = new JsonObject();
        user.addProperty("uid", "123456");
        payloadJObj.add("user", user);

        payloadJObj.addProperty("event", event);
        payloadJObj.addProperty("namespace", "BidirectionalTTS");

        JsonObject req_params = new JsonObject();
        req_params.addProperty("speaker", tts.getVoiceId());
        req_params.addProperty("text", text);
        req_params.addProperty("model", "seed-tts-1.1");

        JsonObject audio_params = new JsonObject();
        audio_params.addProperty("format", "pcm");
        audio_params.addProperty("sample_rate", task.getSampleRate() );
        audio_params.addProperty("enable_timestamp", true);

        // 音量，取值范围[-50,100]，100代表2.0倍，-50代表0.5倍。
        if(tts.getLoudness()!=null) {
            audio_params.addProperty("loudness_rate", tts.getLoudness());
        }
        // 语速，取值范围[-50,100]，100代表2.0倍，-50代表0.5倍。
        if(tts.getSpeed()!=null) {
            audio_params.addProperty("speech_rate", tts.getSpeed());
        }

        req_params.add("audio_params", audio_params);

        //  additions
        JsonObject additions_params = new JsonObject();
        // 是否开启markdown解析过滤，为true时，解析并过滤markdown语法，例如，你好，会读为“你好”，为false时，不解析不过滤，例如，你好，会读为“星星‘你好’星星”
        additions_params.addProperty("disable_markdown_filter", true);
        // 开启emoji表情在文本中不过滤显示，默认为False
        additions_params.addProperty("disable_emoji_filter", true);
        additions_params.addProperty("enable_latex_tn", false);
        // 是否过滤括号内的部分，0为不过滤，100为过滤
        additions_params.addProperty("max_length_to_filter_parenthesis", 100);

        // cache config
//        JsonObject cacheConfig_params = new JsonObject();
//        cacheConfig_params.addProperty("text_type", 1);
//        cacheConfig_params.addProperty("use_cache", true);
//        additions_params.add("cache_config", cacheConfig_params);
        req_params.addProperty("additions", additions_params.toString());

        payloadJObj.add("req_params", req_params);
        return payloadJObj.toString().getBytes();
    }

    private boolean finishSession(WebSocket webSocket, String msgId) {
        log.debug("finish tts session, roomId {}, sessionId {}" , roomId, msgId);
        return VolcanTTSUtils.finishSession(webSocket, msgId);
    }

    @Override
    public int order() {
        return 3;
    }

}
