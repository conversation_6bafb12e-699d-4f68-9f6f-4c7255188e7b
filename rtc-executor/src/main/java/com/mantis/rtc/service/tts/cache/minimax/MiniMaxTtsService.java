package com.mantis.rtc.service.tts.cache.minimax;

import com.alibaba.fastjson.JSON;
import com.mantis.micor.pojo.CallConfig;
import com.mantis.micor.pojo.CallTask;
import com.mantis.rtc.pojo.Constants;
import com.mantis.rtc.service.tts.cache.TtsService;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;

import org.apache.commons.codec.DecoderException;
import org.apache.commons.codec.binary.Hex;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.Base64;

import static com.mantis.rtc.pojo.Constants.JSON_MEDIA_TYPE;

@Service
@Slf4j
public class MiniMaxTtsService implements TtsService {
    @Autowired
    @Qualifier("volTTSWebsocketClient")
    private OkHttpClient client;


    @Override
    public boolean accept(CallTask task) {
        return task.getCallConfig().getTts().getVendor().equalsIgnoreCase(Constants.TTS_VENDOR_MINIMAX);
    }

    @Override
    public String tts(String query, CallTask task) {
        CallConfig.MinimaxTTS minimax = task.getCallConfig().getTts().getMinimax();
        
        TtsRequest ttsRequest = TtsRequest.builder()
                .model(minimax.getModel())
                .language_boost("Chinese")
                .text(query)
                .stream(false)
                .voice_setting(VoiceSetting.builder()
                        .voice_id(minimax.getVoiceId())
                        .speed(1)
                        .vol(1)
                        .pitch(0)
                        .emotion("neutral")
                        .build())
                .audio_setting(AudioSetting.builder()
                        .sample_rate(task.getSampleRate())
                        .format("pcm")
                        .channel(1)
                        .build())
                .build();

        long startTime = System.currentTimeMillis();

        RequestBody requestBody = RequestBody.create(JSON_MEDIA_TYPE, JSON.toJSONString(ttsRequest));
        Request request = new Request.Builder()
                .url("https://api.minimax.chat/v1/t2a_v2?GroupId=" + minimax.getGroupId())
                .post(requestBody)
                .header("Authorization", "Bearer " + minimax.getApiKey())
                .header("Content-Type", "application/json")
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                log.error("fail to call minimax tts, roomId {}, query {}, response code {}", task.getRoomId(), query, response.code());
                return null;
            }

            ResponseBody body = response.body();
            if (body != null) {
                String resp = body.string();
                log.debug("minimax tts response, roomId {}, query {}, response {}", task.getRoomId(), query, resp);
                TtsResult ttsResult = JSON.parseObject(resp, TtsResult.class);
                if (ttsResult != null && ttsResult.getData() != null) {
                    log.info("minimax tts result, roomId {}, query {}, taken {} ms", task.getRoomId(), query, (System.currentTimeMillis() - startTime));

                    int statusCode = ttsResult.getBase_resp().status_code;
                    if(statusCode != 0) {
                        log.error("minimax tts error, roomId {}, query {}, msg {}", task.getRoomId(), query, ttsResult.getBase_resp().getStatus_msg());
                        return null;
                    }

                    try {
                        byte[] audioBytes = Hex.decodeHex(ttsResult.getData().getAudio());
                        return Base64.getEncoder().encodeToString(audioBytes);
                    } catch (DecoderException e) {
                        log.error("Failed to decode hex audio data, roomId {}, query {}", task.getRoomId(), query, e);
                        return null;
                    }
                }
            }
        } catch (IOException e) {
            log.error("exception when call minimax tts, roomId {}, query {}", task.getRoomId(), query, e);
        }
        return null;
    }

    @Data
    @Builder
    public static class TtsRequest {
        private String model;
        private String text;
        private boolean stream;
        private String language_boost;
        private VoiceSetting voice_setting;
        private AudioSetting audio_setting;
    }

    @Data
    @Builder
    public static class VoiceSetting {
        private String voice_id;
        private Integer speed;
        private Integer vol;
        private Integer pitch;
        private String emotion;
    }

    @Data
    @Builder
    public static class AudioSetting {
        private Integer sample_rate;
        private String bitrate;
        private String format;
        private Integer channel;
    }

    @Data
    public static class TtsResult {
        private TtsResultData data;
        private String trace_id;
        private String extra_info;
        private TtsResultBaseResp base_resp;
    }

    @Data
    public static class TtsResultBaseResp {
        private int status_code;
        private String status_msg;
    }

    @Data
    public static class TtsResultData {
        private String audio;
        private int status;
    }
} 