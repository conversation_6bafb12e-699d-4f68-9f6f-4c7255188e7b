package com.mantis.rtc.pojo.chat;

import com.mantis.rtc.pojo.BaseVO;
import lombok.Data;

import java.util.List;

@Data
public class VolcSearchKnowledgeRespDTO extends BaseVO {
    private Integer code;
    private String message;
    private String request_id;
    private VolcSearchKnowledgeData data;


    @Data
    public static class VolcSearchKnowledgeData extends BaseVO{
        private List<VolcSearchKnowledge> result_list;
    }


    @Data
    public static class VolcSearchKnowledge extends BaseVO{
        private String id;
        private String content;
        private Double score;
        private Double rerank_score;
        private String doc_info;
        private String original_question;
        private List<TableChunkFields> table_chunk_fields;
    }

    @Data
    public static class TableChunkFields extends BaseVO{
        private String field_name;
        private String field_value;
    }
}
