package com.mantis.rtc.pojo.chat;

import lombok.Data;

/**
 * ResponseAPI
 * {
 * "type": "response.completed",
 * "response": {
 * "created_at": 1752982069,
 * "id": "resp_02175298206942527857cd728c783ddf90d74b61664d1222011d2",
 * "max_output_tokens": 32768,
 * "model": "doubao-seed-1-6-flash-250615",
 * "object": "response",
 * "output": [{
 * "type": "message",
 * "role": "assistant",
 * "content": [{
 * "type": "output_text",
 * "text": "在呢同学，我是您的课程助理晓敏，看您今天还没听课，今天课程是中医按摩手法，能解决很多身体问题，链接已发微信，有空看看。"
 * }],
 * "status": "completed",
 * "id": "msg_02175298206961800000000000000000000ffffac15585b7d8512"        * 		}],
 * "previous_response_id": "resp_0217529396065984225c495f9ba9202d9c7a7bd20f6f92fea5a84",
 * "thinking": {
 * "type": "disabled"
 * },
 * "service_tier": "default",
 * "status": "completed",
 * "usage": {
 * "input_tokens": 1657,
 * "output_tokens": 41,
 * "total_tokens": 1698,
 * "input_tokens_details": {
 * "cached_tokens": 1561            * 			},
 * "output_tokens_details": {
 * "reasoning_tokens": 0
 * }
 * },
 * "store     true
 * },
 * "sequence_number": 48
 * }
 *
 * <AUTHOR>
 */
@Data
public class ResponseComplete {
    private ResponseObj response;
    private String type;


    @Data
    public static class ResponseObj {
        private long created_at;
        private ResponseUsage usage;
    }

    @Data
    public static class ResponseUsage {
        private long input_tokens;
        private long output_tokens;
        private long total_tokens;
        private ResponseInputTokenDetail input_tokens_details;
        private ResponseOutputTokenDetail output_tokens_details;
    }

    @Data
    public static class ResponseInputTokenDetail {
        private long cached_tokens;
    }

    @Data
    public static class  ResponseOutputTokenDetail {
        private long reasoning_tokens;
    }

}
