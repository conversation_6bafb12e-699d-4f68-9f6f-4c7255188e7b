package com.mantis.rtc.pojo.chat;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class Usage {
    @JsonProperty("prompt_tokens")
    private long promptTokens;
    @JsonProperty("completion_tokens")
    private long completionTokens;
    @JsonProperty("total_tokens")
    private long totalTokens;

    /**
     * token缓存的计数
     */
    @JsonProperty("prompt_cache_hit_tokens")
    private long promptCacheHitTokens;
    @JsonProperty("prompt_cache_miss_tokens")
    private long promptCacheMissTokens;

    /**
     * daobao prompt_tokens_details
     * https://www.volcengine.com/docs/82379/1396491#last-history-tokens-%E6%A8%A1%E5%BC%8F-2
     */
    @JsonProperty("prompt_tokens_details")
    private promptTokensDetails promptTokensDetails;

    @Data
    public static class promptTokensDetails {
        // prompt token中 cache的部分
        @JsonProperty("cached_tokens")
        private int cachedTokens;
    }
}
