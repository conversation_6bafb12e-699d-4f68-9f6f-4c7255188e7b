package com.mantis.rtc.pojo.chat;

public class ChatCounter {
    private Usage usage;
    /**
     * 方始括号开始符号[的个数
     */
    private int bucketCount = 0;

    public boolean isFilterMode(){
        return (this.bucketCount > 0);
    }

    public void incrBucketCount() {
        this.bucketCount = this.bucketCount + 1;
    }

    public void decrBucketCount() {
        this.bucketCount = this.bucketCount - 1;
    }


    public Usage getUsage() {
        return usage;
    }

    public void setUsage(Usage usage) {
        this.usage = usage;
    }
}
