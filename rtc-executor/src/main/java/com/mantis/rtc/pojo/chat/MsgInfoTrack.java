package com.mantis.rtc.pojo.chat;

import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
public class MsgInfoTrack {

    private String msgId;
    private String vadCompleteMsg;
    private String asrFinalMsg;
    private String volcanAsrFinalMsg;
    /**
     * 本次回复是否被打断
     */
    private boolean isInterrupted;
    // 首次开始说话时间
    private Long speakTime;
    // 说话结束时间
    private Long speakEndTime;
    // 加上静音等待的结束时间
    private Long speakCompleteTime;
    // 说话持续时间, 单位毫秒
    private Long speakDuration;
    // asr首token时间
    private Long asrFirstTokenTime;
    private Long volcanAsrFirstTokenTime;
    // 送asr数据完成，开始计算asr尾包延迟
    private Long sentAsrCompleteTime;
    private Long sentVolcanAsrCompleteTime;
    // asr尾包到达时间
    private Long asrCompleteTime;
    private Long volcanAsrCompleteTime;

    private Long llmBeginTime;
    // llm首token时间
    private Long llmFirstTokenTime;
    // tts首token时间
    private Long ttsFirstTokenTime;
    // 知识库调用耗时
    private Long knowledgeTime;

    // 记录本次消息是否有效
    private boolean isValidMsg = false;
    // 情绪
    private String emotion;

    /**
     * tts 语音停止时间
     */
    private long ttsEndTime = 0;

    public MsgInfoTrack(String msgId) {
        this.msgId = msgId;
    }

    public void addSpeechTime(long time) {
        if(ttsEndTime == 0) {
            ttsEndTime = System.currentTimeMillis();
        } else {
            ttsEndTime = ttsEndTime + time;
        }
    }

    /**
     * 判断AI是否已经说完
     * @return
     */
    public boolean isAiSpeechEnd(){
        return ttsEndTime > 0 && System.currentTimeMillis() - ttsEndTime > 0;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("timer_info, isValidMsg: ").append (isValidMsg).append(", ");
        sb.append("msg: ").append (asrFinalMsg).append(", ");
        sb.append("interrupted: ").append (isInterrupted).append(", ");

//        if (asrFirstTokenTime != null && speakTime != null) {
//            sb.append("asr first token delay: ").append (asrFirstTokenTime - speakTime).append(", ");
//        }

        if (sentAsrCompleteTime != null && asrCompleteTime != null) {
            sb.append("asr tail token delay: ").append (asrCompleteTime - sentAsrCompleteTime).append(", ");
        }

//        if(StringUtils.isNotBlank(asrFinalMsg) && StringUtils.isNotBlank(vadCompleteMsg)){
//            if(asrFinalMsg.equals(vadCompleteMsg)) {
//                sb.append("msg same: ").append(asrFinalMsg).append(", ");
//            }else {
//                sb.append("msg not same: ").append(vadCompleteMsg).append("/").append(asrFinalMsg).append(", ");
//            }
//        }

        if(knowledgeTime != null) {
            sb.append("knowledge delay: ").append (knowledgeTime).append(", ");
        }

        if(llmFirstTokenTime != null && llmBeginTime != null) {
            sb.append("llm firs token delay: ").append (llmFirstTokenTime - llmBeginTime).append(", ");
        }

        if(ttsFirstTokenTime != null && llmFirstTokenTime != null && speakEndTime != null) {
            sb.append("tts delay: ").append (ttsFirstTokenTime - llmFirstTokenTime).append(", ");
            sb.append("total delay for speak complete: ").append (ttsFirstTokenTime - speakCompleteTime).append(", ");
            sb.append("total delay by speak end: ").append (ttsFirstTokenTime - speakEndTime);
        }
        return sb.toString();

    }
}
