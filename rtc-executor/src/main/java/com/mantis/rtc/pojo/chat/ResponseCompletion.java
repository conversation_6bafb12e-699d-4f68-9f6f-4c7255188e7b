package com.mantis.rtc.pojo.chat;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * chat
 *
 * <AUTHOR>
 */
@Data
@Builder
@Slf4j
@AllArgsConstructor
@NoArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class ResponseCompletion extends BaseCompletion implements Serializable {

    @NonNull
    private String model;

    @NonNull
    private List<Message> input;

    // 上一个模型回复的唯一标识符。使用该标识符可以实现多轮对话。
    private String previous_response_id;

    @NonNull
    @JsonProperty("thinking")
    private Thinking thinking;

    /**
     * 使用什么取样温度，0到2之间。越高越奔放。越低越保守。
     * <p>
     * 不要同时改这个和topP
     */
    @Builder.Default
    private double temperature = 0.9;

    /**
     * 0-1
     * 建议0.9
     * 不要同时改这个和temperature
     */
    @JsonProperty("top_p")
    private Double topP;

    /**
     * 是否流式输出.
     * default:false
     */
    @Builder.Default
    private boolean stream = true;

    /**
     * 3.5 最大支持4096
     * 4.0 最大32k
     */
    @JsonProperty("max_output_tokens")
    private Integer maxTokens;

}


