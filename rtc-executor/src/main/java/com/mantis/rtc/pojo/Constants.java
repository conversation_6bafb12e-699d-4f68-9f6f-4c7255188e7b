package com.mantis.rtc.pojo;

import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;


import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@Slf4j
public class Constants {

    public final static ThreadPoolExecutor executor = new ThreadPoolExecutor(3, 20,
            60L, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(20),
            new ThreadPoolExecutor.CallerRunsPolicy());


    public final static ThreadPoolExecutor callBackExecutor = new ThreadPoolExecutor(2, 10,
            60L, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(20),
            new ThreadPoolExecutor.CallerRunsPolicy());

    /**
     * ASR/TTS 初始化线程池
     */
    public final static ThreadPoolExecutor ASR_TTS_EXECUTOR = new ThreadPoolExecutor(6, 10,
            60L, TimeUnit.SECONDS,
            new ArrayBlockingQueue<>(20),
            new ThreadPoolExecutor.CallerRunsPolicy());

    public static final MediaType JSON_MEDIA_TYPE = MediaType.parse("application/json; charset=utf-8");

    public static final String ASR_VENDOR_VOLCAN = "volcan";
    public static final String ASR_VENDOR_TENCENT = "tencent";

    public static final String TTS_VENDOR_VOLCAN = "volcan";
    public static final String TTS_VENDOR_COSYVOICE = "cosyvoice";
    public static final String TTS_VENDOR_MINIMAX = "minimax";

    public static final String[] EMPTY_ASR_RES = new String[]{
            "喂，在吗？",
            "喂，能听到吗",
    };
    public static final String[] SIMPLE_RES = new String[]{
            "喂，您能听到吗？",
            "在呢",
            "我在呢",
            "在呢，能听到吗？",
            "我在呢，您能听到吗？"
    };


    public static final Map<String, String> SIMPLE_RES2 = new HashMap<>() {
        {
            put("嗯", "嗯。。。, 我想想啊，我们课程每天都可以看的");
        }
    };


    /**
     * 首先，代码的循环是从i=0开始，每次增加2，这是因为音频数据通常是16位的，每个样本占两个字节。所以循环处理每个样本是正确的。接下来，将两个字节组合成一个short类型的样本。这里可能有问题，因为字节是有符号的，而Java中byte的范围是-128到127，但处理音频时通常是无符号的。用户代码中使用的是(data[i] & 0xFF)，这应该是对的，因为这样可以正确地将字节转换为无符号的整数值。然后，左移8位后与前面的结果进行或操作，得到16位的short。这部分看起来没问题。  接下来，应用增益，乘以gain，然后限制在-32768到32767之间，这也是正确的，因为short的范围就是-32768到32767。然后将结果写回byte数组，低字节和高字节分别存储。这里可能的问题是关于字节的顺序。比如，假设原始数据是小端模式（低位在前，高位在后），那么代码是正确的，但如果是大端模式，可能就会出错。不过通常音频数据是小端存储的，所以这里可能没问题，但需要确认上下文。
     * @param data
     * @param length
     * @param gain
     */
    public static void adjustVolume(byte[] data, int length, float gain) {
        for (int i = 0; i < length; i += 2) {
            short sample = (short) ((data[i] & 0xFF) | (data[i+1] << 8));
            sample = (short) (sample * gain);
            sample = (short) Math.max(-32768, Math.min(32767, sample));
            data[i] = (byte) (sample & 0xFF);
            data[i+1] = (byte) ((sample >> 8) & 0xFF);
        }
    }
}
