package com.mantis.rtc.pojo.chat;


import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.logging.HttpLoggingInterceptor;
import okhttp3.sse.EventSource;
import okhttp3.sse.EventSourceListener;
import okhttp3.sse.EventSources;

import java.net.Proxy;
import java.util.Objects;
import java.util.concurrent.TimeUnit;


/**
 * openai 流式传输客户端
 *
 * <AUTHOR>
 */

@Slf4j
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ChatGPTStream {
    private String url;
    private String apiKey;

    private OkHttpClient okHttpClient;
    /**
     * 连接超时
     */
    @Builder.Default
    private long timeout = 10;

    /**
     * 网络代理
     */
    @Builder.Default
    private Proxy proxy = Proxy.NO_PROXY;

    /**
     * 初始化
     */
    public ChatGPTStream init() {
        HttpLoggingInterceptor loggingInterceptor = new HttpLoggingInterceptor();
        loggingInterceptor.setLevel(HttpLoggingInterceptor.Level.NONE);
        OkHttpClient.Builder client = new OkHttpClient.Builder();
        client.addInterceptor(loggingInterceptor)
                .connectTimeout(timeout, TimeUnit.SECONDS)
                .writeTimeout(timeout, TimeUnit.SECONDS)
                .readTimeout(timeout, TimeUnit.SECONDS);

        if (Objects.nonNull(proxy)) {
            client.proxy(proxy);
        }
        okHttpClient = client.build();
        return this;
    }

    /**
     * 对话场景的流式输出
     */
    public void streamChatCompletion(String roomId, String msgId, BaseCompletion chatCompletion,
                                     EventSourceListener eventSourceListener) {
        log.debug("begin to streamChatCompletion, roomId {}, msgId {}", roomId, msgId);
        String requestBody = "";
        try {
            EventSource.Factory factory = EventSources.createFactory(okHttpClient);
            ObjectMapper mapper = new ObjectMapper();
            requestBody = mapper.writeValueAsString(chatCompletion);
            log.info("request llm: {}, roomId {}, msgId {}, perf", requestBody, roomId, msgId);

            Request request = new Request.Builder()
                    .url(url)
                    .post(RequestBody.create(MediaType.parse(org.springframework.http.MediaType.APPLICATION_JSON_VALUE), requestBody))
                    .header("Authorization", "Bearer " + apiKey)
                    .build();
            factory.newEventSource(request, eventSourceListener);

        } catch (Exception e) {
            log.error("request llm: {}, roomId {}, 请求大模型出错", requestBody, roomId, e);
        }
    }
}
