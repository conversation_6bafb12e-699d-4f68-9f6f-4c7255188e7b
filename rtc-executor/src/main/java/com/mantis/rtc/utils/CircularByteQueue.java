package com.mantis.rtc.utils;

import lombok.extern.slf4j.Slf4j;

/**
 * 我需要考虑以下几种情况：
 * 1. **队列为空**：如果队列中没有数据，应该抛出异常或者返回一个空数组。
 * 2. **请求的数量大于队列中当前的数据量**：这种情况下，应该只能取出当前队列中的所有数据，并且可能需要告知调用者实际取出的数据量。
 * 3. **请求的数量小于或等于队列中当前的数据量**：
 * - 如果 `head` 和 `tail` 指针在同一段连续的内存中，可以直接取出。
 * - 如果 `head` 和 `tail` 指针因为环形特性而不在同一段连续的内存中，需要分两部分取出，然后合并成一个数组。
 *
 * 在实现环形队列时，head 和 tail 指针分别表示队列的起始位置和结束位置。
 * 在 enqueue 操作中，通常情况下，tail 指针负责添加数据并移动，而 head 指针在 dequeue 操作时移动。
 * 当队列已满时，同时移动head与tail来放弃旧的数据，写入新数据。
 */

@Slf4j
public class CircularByteQueue {
    private final byte[] buffer;
    private int head;
    private int tail;
    private int size;
    private final int capacity;
    private String name = null;

    public CircularByteQueue(int capacity, String name) {
        this.capacity = capacity;
        this.buffer = new byte[capacity];
        this.head = 0;
        this.tail = 0;
        this.size = 0;
        this.name = name;
    }

    public boolean isEmpty() {
        return size == 0;
    }

    public boolean isFull(int inputSize) {
        return size + inputSize > capacity;
    }

    public int size() {
        return size;
    }

    /**
     * 写入指定长度的数据
     * @param data
     */
    public void enqueue(byte[] data) {
        if (data == null || data.length == 0) {
            return;
        }

        // 如果队列满，放弃整批数据
        if(isFull(data.length)) {
            log.error("queue is full, skip data, name {}", name);
            return;
        }

        try {
            if (tail + data.length <= capacity) {
                System.arraycopy(data, 0, buffer, tail, data.length);
                tail = (tail + data.length) % capacity;
                size = size + data.length;
            } else {
                // 计算第二次剩余的长度
                int left = data.length - (capacity - tail);

                // 先尽可能复制, 使用剩余可用的长度
                int len = capacity - tail;
                System.arraycopy(data, 0, buffer, tail, len);
                tail = (tail + len) % capacity;
                size = size + len;
                // 再写入剩余部分
                System.arraycopy(data, len, buffer, tail, left);
                tail = (tail + left) % capacity;
                size = size + left;
            }
        }catch (Exception e) {
            log.error("fail to enqueue, h {}, t {}", head, tail, e);
        }finally {
            log.trace("enqueue complete, h {}, t {}", head, tail);
        }
    }

    /**
     * 读取指定长度的数据
     * @param length
     * @return
     */
    public byte[] dequeue(int length) {
        if (isEmpty()) {
            return null;
        }

        // 如果不够读取
        if(size < length) {
            log.trace("no enough data, skip dequeue, name {}", name);
            return null;
        }

        try{
            // 可以一步读取
            if (head + length <= capacity) {
                byte[] result = new byte[length];
                System.arraycopy(buffer, head, result, 0, length);
                head = (head + length) % capacity;
                size = size - length;
                return result;
            }else {
                // 需要分步读取， 第一步读取的长度
                int len = capacity - head;
                // 第二步读取的长度
                int left = length - (capacity - head);

                byte[] result = new byte[length];
                System.arraycopy(buffer, head, result, 0, len);
                head = (head + len) % capacity;
                size = size - len;

                System.arraycopy(buffer, head, result, len, left);
                head = (head + left) % capacity;
                size = size - left;
                return result;
            }
        }catch (Exception e) {
            log.error("fail to dequeue, h {}, t {}", head, tail, e);
        } finally {
            log.trace("dequeue complete, h {}, t {}", head, tail);
        }
        return null;
    }

    /**
     * 读取数据，如果不够length，返回剩余的所有
     * @param length
     * @param fetchLeft
     * @return
     */
    public byte[] dequeue(int length, boolean fetchLeft) {
        if (isEmpty()) {
            return null;
        }
        int len = length;
        if(fetchLeft) {
            len = Math.min(length, size);
        }

        return dequeue(len);
    }

    public void clear() {
        head = 0;
        tail = 0;
        size = 0;
    }

    @Override
    public String toString() {
        if(isEmpty()) {
            return "[]";
        }
        StringBuilder sb = new StringBuilder();
        sb.append("[");
        for (int i = 0; i < size; i++) {
            sb.append(buffer[(head + i) % capacity]);
            if (i < size - 1) {
                sb.append(", ");
            }
        }
        sb.append("]");
        return sb.toString();
    }

    public static void main(String[] args) {
        CircularByteQueue queue = new CircularByteQueue(6, "test");

        queue.enqueue(new byte[]{1, 2, 3, 4, 5});

        byte[] data2 = queue.dequeue(2);
//        System.out.println(Arrays.toString(data2)); // 输出: [1, 2, 3, 4]
//        System.out.println(queue); // 输出: [5]

        // 队列满，未写入
        queue.enqueue(new byte[]{8, 9});
//        System.out.println(queue); // 输出: [5]

        // 不够读取
        byte[] data3 = queue.dequeue(2);
//        System.out.println(Arrays.toString(data3)); // 输出: [12, 13]
    }
}