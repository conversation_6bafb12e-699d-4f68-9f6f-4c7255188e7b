package com.mantis.rtc.utils;

import lombok.extern.slf4j.Slf4j;

/**
 * 音频重采样工具类
 * 用于将48000采样率双声道音频转换为16000采样率单声道音频
 * 
 * <AUTHOR>
 */
@Slf4j
public class AudioResampler {
    
    /**
     * 源采样率：48000Hz
     */
    public static final int SOURCE_SAMPLE_RATE = 48000;
    
    /**
     * 目标采样率：16000Hz
     */
    public static final int TARGET_SAMPLE_RATE = 16000;
    
    /**
     * 源声道数：双声道
     */
    public static final int SOURCE_CHANNELS = 2;
    
    /**
     * 目标声道数：单声道
     */
    public static final int TARGET_CHANNELS = 1;
    
    /**
     * 重采样比例：48000/16000 = 3
     */
    public static final int RESAMPLE_RATIO = SOURCE_SAMPLE_RATE / TARGET_SAMPLE_RATE;
    
    /**
     * 声道转换比例：2/1 = 2
     */
    public static final int CHANNEL_RATIO = SOURCE_CHANNELS / TARGET_CHANNELS;
    
    /**
     * 将48000采样率双声道音频数据转换为16000采样率单声道音频数据
     * 
     * @param sourceData 源音频数据（48000Hz, 双声道, 16bit）
     * @return 转换后的音频数据（16000Hz, 单声道, 16bit）
     */
    public static byte[] resample(byte[] sourceData) {
        if (sourceData == null || sourceData.length == 0) {
            log.warn("源音频数据为空，返回空数组");
            return new byte[0];
        }
        
        // 计算源数据的采样点数（每个采样点2字节，双声道）
        int sourceSamples = sourceData.length / (SOURCE_CHANNELS * 2);
        
        // 计算目标数据的采样点数（重采样后）
        int targetSamples = sourceSamples / RESAMPLE_RATIO;
        
        // 计算目标数据长度（单声道，每个采样点2字节）
        int targetLength = targetSamples * TARGET_CHANNELS * 2;
        
        byte[] targetData = new byte[targetLength];
        
        log.debug("音频重采样: 源数据长度={}, 源采样点数={}, 目标采样点数={}, 目标数据长度={}", 
                sourceData.length, sourceSamples, targetSamples, targetLength);
        
        // 执行重采样和声道转换
        for (int i = 0; i < targetSamples; i++) {
            // 计算源数据中的对应位置
            int sourceIndex = i * RESAMPLE_RATIO;
            
            // 计算源数据中该采样点的字节位置
            int sourceByteIndex = sourceIndex * SOURCE_CHANNELS * 2;
            
            // 计算目标数据中该采样点的字节位置
            int targetByteIndex = i * TARGET_CHANNELS * 2;
            
            // 合并双声道数据为单声道（取平均值）
            short leftChannel = getSample(sourceData, sourceByteIndex);
            short rightChannel = getSample(sourceData, sourceByteIndex + 2);
            
            // 计算平均值作为单声道数据
            short monoChannel = (short) ((leftChannel + rightChannel) / 2);
            
            // 写入目标数据
            setSample(targetData, targetByteIndex, monoChannel);
        }
        
        return targetData;
    }
    
    /**
     * 从字节数组中获取16位采样点数据
     * 
     * @param data 字节数组
     * @param offset 偏移量
     * @return 采样点值
     */
    private static short getSample(byte[] data, int offset) {
        if (offset + 1 >= data.length) {
            return 0;
        }
        // 小端序读取（低字节在前）
        return (short) ((data[offset] & 0xFF) | (data[offset + 1] << 8));
    }
    
    /**
     * 将16位采样点数据写入字节数组
     * 
     * @param data 字节数组
     * @param offset 偏移量
     * @param sample 采样点值
     */
    private static void setSample(byte[] data, int offset, short sample) {
        if (offset + 1 >= data.length) {
            return;
        }
        // 小端序写入（低字节在前）
        data[offset] = (byte) (sample & 0xFF);
        data[offset + 1] = (byte) ((sample >> 8) & 0xFF);
    }
    
    /**
     * 计算重采样后的数据长度
     * 
     * @param sourceLength 源数据长度
     * @return 目标数据长度
     */
    public static int calculateTargetLength(int sourceLength) {
        int sourceSamples = sourceLength / (SOURCE_CHANNELS * 2);
        int targetSamples = sourceSamples / RESAMPLE_RATIO;
        return targetSamples * TARGET_CHANNELS * 2;
    }
    
    /**
     * 验证源音频数据格式是否符合要求
     * 
     * @param sourceData 源音频数据
     * @return 是否符合格式要求
     */
    public static boolean isValidSourceFormat(byte[] sourceData) {
        if (sourceData == null || sourceData.length == 0) {
            return false;
        }
        
        // 检查数据长度是否为双声道16位采样的整数倍
        return sourceData.length % (SOURCE_CHANNELS * 2) == 0;
    }
}