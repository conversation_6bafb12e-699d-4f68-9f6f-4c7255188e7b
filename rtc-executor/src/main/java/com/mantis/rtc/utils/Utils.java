package com.mantis.rtc.utils;

import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Random;

@Slf4j
public class Utils {
    public static String calcRandomWeiRes(String[] inputs) {
        int length = inputs.length;
        int random = generateRandomNumber(0, length - 1);
        return inputs[random];
    }

    public static int generateRandomNumber(int min, int max) {
        Random random = new Random();
        return random.nextInt(max - min + 1) + min;
    }

    /**
     * 休眠
     *
     * @param time 毫秒
     */
    public static void sleep(long time) {
        try {
            Thread.sleep(time);
        } catch (InterruptedException e) {
            log.error("sleep interrupted", e);
        }
    }

    /**
     * 计算中文字符串的 MD5 哈希值作为 Redis 键
     *
     * @param chineseString 输入的中文字符串
     * @return MD5 哈希值作为字符串
     */
    public static String getChineseStringMD5Key(String chineseString) {
        try {
            // 获取 MD5 消息摘要实例
            MessageDigest digest = MessageDigest.getInstance("MD5");
            // 将中文字符串转换为字节数组，并使用 UTF-8 编码
            byte[] hash = digest.digest(chineseString.getBytes(StandardCharsets.UTF_8));
            StringBuilder hexString = new StringBuilder(2 * hash.length);
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            log.error("将中文转化为md5失败, 输入: {} ", chineseString, e);
        }
        return null;
    }
}