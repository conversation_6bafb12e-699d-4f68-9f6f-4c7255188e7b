package com.mantis.rtc.utils;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class RegularExpression {
    /**
     * 学员主动消息挂断消息
     */
    public static final Pattern CLOSE_CHAT_PATTERN = Pattern.compile("(好的|嗯|嗯嗯|好|行|就这样|哦|挂机|先挂了|挂了吧|挂吧|拜拜|再见|过会打|等会打|一会打)");
    /**
     * 骂人\不耐烦
     */
    public static final Pattern ILLAGE_CHAT_PATTERN = Pattern.compile("(别打了|烦不烦|不要再打|别给我打|操你|流氓|无赖|你妈|白痴|混蛋|他妈的|神经病|天天打|老是打|不要打|我操|别再给我打|你他妈|你大爷|狗屁|诈骗|鸡巴|傻逼)");
    /**
     * 机器人识别
     */
    public static final Pattern ROBOT_CHAT_PATTERN = Pattern.compile("(#hook#|再见|拜拜)");

    /**
     * 匹配正则
     * @param pattern
     * @param msg
     * @return
     */
    public static boolean isCheckPass(Pattern pattern, String msg) {
        Matcher matcher = pattern.matcher(msg);
        if (matcher.find()) {
            return true;
        }
        return false;
    }


}
