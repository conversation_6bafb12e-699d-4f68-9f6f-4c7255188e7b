package com.mantis.rtc.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Data
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "mantis.vad")
public class VadConfig {
    private Boolean record;
    private Boolean allowInterrupt;
}