package com.mantis.rtc.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.context.annotation.Configuration;

@Slf4j
@Data
@RefreshScope
@Configuration
@ConfigurationProperties(prefix = "mantis.llm")
public class LLMConfig {
    private Float temperature = 0.7f;
    private Float frequencyPenalty;
    private Integer historyLength = 30;

    // 大模型供应商
    private String vendor;
    private LLMInfo tencent;
    private LLMInfo theturbo;
    private LLMInfo deepseek;
    private LLMInfo volcan;

    @Data
    public static class LLMInfo {
        private String url;
        private String apiKey;
    }

}