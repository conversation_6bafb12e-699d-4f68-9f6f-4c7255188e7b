package com.mantis.rtc.config;

import lombok.Data;
import org.springframework.context.annotation.Configuration;

import java.util.HashMap;
import java.util.Map;

@Data
@Configuration
public class HostMappingConfig {
    /**
     * 服务器内网地址与外网地址的映射，如果executor运行服务器在外网，需要配置
     */
    private Map<String, String> hostMapping = new HashMap<>() {
        {
            put("***********", "*************");
        }
    };
}
