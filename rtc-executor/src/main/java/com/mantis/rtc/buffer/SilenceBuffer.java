package com.mantis.rtc.buffer;

// import com.mantis.rtc.service.BufferService;
// import com.mantis.rtc.utils.CircularByteQueue;
// import lombok.extern.slf4j.Slf4j;
// import org.springframework.stereotype.Service;

// import java.util.concurrent.locks.Lock;
// import java.util.concurrent.locks.ReentrantLock;

//@Slf4j
//@Service
public class SilenceBuffer {//implements BufferService {
//    /**
//     * 静默期间语音数据缓冲区
//     *
//     */
//
//    private CircularByteQueue queue = new CircularByteQueue(30000, "silence_buffer");
//    private final Lock silenceLock = new ReentrantLock();
//
//
//    public void enqueue(byte[] data) {
//        silenceLock.lock();
//        try {
//            queue.enqueue(data);
//            log.debug("rtc data buffered for silence, size {}", queue.size());
//        } finally {
//            silenceLock.unlock();
//        }
//    }
//
//    public byte[] dequeue(int length) {
//        silenceLock.lock();
//        try {
//            byte[] dequeue = queue.dequeue(length);
//            if(dequeue != null) {
//                log.debug("got data for silence, dequeue, size {}", queue.size());
//            }
//            return dequeue;
//        } finally {
//            silenceLock.unlock();
//        }
//    }
//
//    public byte[] dequeue(int length, boolean fetchLeft) {
//        silenceLock.lock();
//        try {
//            return queue.dequeue(length, fetchLeft);
//        } finally {
//            silenceLock.unlock();
//        }
//    }
//
//    public int size() {
//        return queue.size();
//    }
//
//    @Override
//    public void clean() {
//        queue.clear();
//    }

}
