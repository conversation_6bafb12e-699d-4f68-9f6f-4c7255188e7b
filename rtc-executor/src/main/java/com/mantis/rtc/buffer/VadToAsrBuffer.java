package com.mantis.rtc.buffer;

import com.mantis.rtc.service.BufferService;
import com.mantis.rtc.utils.CircularByteQueue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
@Service
public class VadToAsrBuffer implements BufferService {
    /**
     * VAD To ASR 的缓冲区，VAD每次产出1024字节。ASR 要求大小为1-16K。
     */

    private CircularByteQueue queue = new CircularByteQueue(32000 * 2, "vad_to_asr_buffer");
    private final Lock lock = new ReentrantLock();


    public void enqueue(byte[] data) {
        lock.lock();
        try {
//            log.info("vad data buffered for asr, length {}", data.length);
            queue.enqueue(data);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 是否满足ASR要求，满足则从缓冲区取出数据，否则返回null
     * @param length
     * @param fetchLeft
     * @return
     */
    public byte[] dequeue(int length, boolean fetchLeft) {
        lock.lock();
        try {
            return queue.dequeue(length, fetchLeft);
        } finally {
            lock.unlock();
        }
    }

    public int size() {
        return queue.size();
    }

    @Override
    public void clean() {
        queue.clear();
    }

    public boolean isEmpty() {
        return queue.isEmpty();
    }

}
