package com.mantis.rtc.buffer;

import com.mantis.rtc.service.BufferService;
import com.mantis.rtc.service.CallContextService;
import com.mantis.rtc.utils.CircularByteQueue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
@Service
public class WelcomeBuffer implements BufferService {
    @Autowired
    private CallContextService contextService;
    /**
     * 欢迎语tts 缓冲区
     *
     */

    private CircularByteQueue queue = new CircularByteQueue(3000000, "welcome_buffer");
    private final Lock welcomeLock = new ReentrantLock();


    public void enqueue(byte[] data) {
        welcomeLock.lock();
        // 标识有需要推送的数据
        contextService.getHasRtcDataToPush().set(true);
        try {
            queue.enqueue(data);
            log.debug("rtc data buffered for welcome, size {}", queue.size());
        } finally {
            welcomeLock.unlock();
        }
    }

    public byte[] dequeue(int length) {
        welcomeLock.lock();
        try {
            byte[] dequeue = queue.dequeue(length);
            if(dequeue != null) {
                log.debug("got data for welcome, dequeue, size {}", queue.size());
            }
            return dequeue;
        } finally {
            welcomeLock.unlock();
        }
    }

    public byte[] dequeue(int length, boolean fetchLeft) {
        welcomeLock.lock();
        try {
            return queue.dequeue(length, fetchLeft);
        } finally {
            welcomeLock.unlock();
        }
    }

    public int size() {
        welcomeLock.lock();
        try {
            return queue.size();
        } finally {
            welcomeLock.unlock();
        }
    }

    @Override
    public void clean() {
        queue.clear();
    }

}
