package com.mantis.rtc.buffer;

import com.mantis.rtc.service.BufferService;
import com.mantis.rtc.service.CallContextService;
import com.mantis.rtc.service.rtc.utils.RtcConstants;
import com.mantis.rtc.utils.CircularByteQueue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
@Service
public class TtsToRtcBuffer implements BufferService {
    @Autowired
    private CallContextService contextService;

    /**
     * tts 到 rtc 的缓冲区
     * RTC要求每次送10ms数据，按照16000采样率，单声道，每次数据包大小为16000*10/1000*2 = 320。设置缓冲区大小为30s。
     */
    private CircularByteQueue queue = new CircularByteQueue(32000 * 30, "tts_to_rtc_buffer");
    private final Lock lock = new ReentrantLock();


    public void enqueue(byte[] data, String msgId) {
        // 累计语音时长
        int bytesPerMs = RtcConstants.RTC_AUDIO_CHANNELS * (contextService.getTask().getSampleRate() / 1000) * 2;
        long pcmDataTimeMs = data.length / bytesPerMs;
        contextService.getMsgInfoTrack(msgId).addSpeechTime(pcmDataTimeMs);

        lock.lock();
        // 标识有需要推送的数据
        contextService.getHasRtcDataToPush().set(true);
        try {
            log.debug("tts data buffer for rtc, length {}, size {}", data.length, size());
            queue.enqueue(data);
        } finally {
            lock.unlock();
        }
    }

    public byte[] dequeue(int length) {
        lock.lock();
        try {
            if (queue.size() < length) {
                return null;
            }
            log.debug("got data for rtc, dequeue, length {}, size {}", length, size());
            return queue.dequeue(length);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 是否满足ASR要求，满足则从缓冲区取出数据，否则返回null
     * @param length
     * @param fetchLeft
     * @return
     */
    public byte[] dequeue(int length, boolean fetchLeft) {
        lock.lock();
        try {
            return queue.dequeue(length, fetchLeft);
        } finally {
            lock.unlock();
        }
    }

    public int size() {
        lock.lock();
        try {
            return queue.size();
        } finally {
            lock.unlock();
        }
    }

    @Override
    public void clean() {
        queue.clear();
    }
}
