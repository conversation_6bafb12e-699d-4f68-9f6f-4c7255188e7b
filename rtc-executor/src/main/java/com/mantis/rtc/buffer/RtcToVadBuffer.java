package com.mantis.rtc.buffer;

import com.mantis.rtc.service.BufferService;
import com.mantis.rtc.utils.CircularByteQueue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@Slf4j
@Service
public class RtcToVadBuffer implements BufferService {
    /**
     * RTC to VAD 缓冲区
     * rtc 10ms 数据，按照16000采样率，单声道，每次数据包大小为320。设置缓冲区大小为5s。
     * VAD 要求每次送1024字节
     *
     */

    private CircularByteQueue queue = new CircularByteQueue(32000 * 5, "rtc_to_vad_buffer");
    private final Lock vadLock = new ReentrantLock();


    public void enqueue(byte[] data) {
        vadLock.lock();
        try {
            queue.enqueue(data);
            log.debug("rtc data buffered for vad, size {}", queue.size());
        } finally {
            vadLock.unlock();
        }
    }

    public byte[] dequeue(int length) {
        vadLock.lock();
        try {
            byte[] dequeue = queue.dequeue(length);
            if(dequeue != null) {
                log.debug("got data for vad, dequeue, size {}", queue.size());
            }
            return dequeue;
        } finally {
            vadLock.unlock();
        }
    }

    public byte[] dequeue(int length, boolean fetchLeft) {
        vadLock.lock();
        try {
            return queue.dequeue(length, fetchLeft);
        } finally {
            vadLock.unlock();
        }
    }

    public int size() {
        return queue.size();
    }

    @Override
    public void clean() {
        queue.clear();
    }

}
