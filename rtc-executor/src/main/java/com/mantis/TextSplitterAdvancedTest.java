package com.mantis;

import java.util.List;

/**
 * 文本拆句工具高级测试类
 * 测试超长文本的拆句功能
 */
public class TextSplitterAdvancedTest {
    
    public static void main(String[] args) {
        // 测试用例：超长文本（超过400字）
        String longText = "人工智能技术的发展历程可以追溯到20世纪50年代，当时科学家们开始探索让机器模拟人类智能的可能性。" +
                "早期的人工智能研究主要集中在符号推理和专家系统上，这些系统通过预定义的规则和知识库来解决特定领域的问题。" +
                "然而，这种方法存在明显的局限性，难以处理复杂和不确定的现实世界问题。" +
                "随着计算能力的提升和大数据时代的到来，机器学习技术开始崭露头角。" +
                "特别是深度学习的兴起，彻底改变了人工智能的发展轨迹。" +
                "深度神经网络通过多层非线性变换，能够自动学习数据中的复杂模式和特征表示。" +
                "在图像识别领域，卷积神经网络（CNN）取得了突破性进展，其性能甚至超越了人类专家。" +
                "在自然语言处理方面，循环神经网络（RNN）和长短期记忆网络（LSTM）为处理序列数据提供了有效方法。" +
                "近年来，Transformer架构的提出进一步推动了自然语言处理技术的发展。" +
                "基于Transformer的大型语言模型如GPT系列、BERT、T5等，在各种NLP任务中都表现出了卓越的性能。" +
                "这些模型通过在海量文本数据上进行预训练，学习到了丰富的语言知识和常识。" +
                "它们不仅能够理解文本的语义，还能生成流畅自然的文本内容。" +
                "在实际应用中，人工智能技术已经渗透到我们生活的方方面面。" +
                "智能手机中的语音助手可以理解用户的语音指令并提供相应服务。" +
                "搜索引擎利用机器学习算法为用户提供更精准的搜索结果。" +
                "推荐系统通过分析用户行为数据，为用户推荐感兴趣的内容和商品。" +
                "自动驾驶汽车使用计算机视觉和传感器融合技术来感知环境并做出驾驶决策。" +
                "医疗诊断系统可以辅助医生分析医学影像，提高诊断的准确性和效率。" +
                "金融风控系统利用机器学习模型来识别欺诈交易和评估信贷风险。" +
                "智能制造系统通过优化生产流程和预测设备故障来提高生产效率。" +
                "然而，人工智能技术的发展也带来了一些挑战和问题。" +
                "算法偏见可能导致不公平的决策结果，影响社会公正。" +
                "隐私保护成为一个重要议题，如何在利用数据的同时保护用户隐私需要仔细平衡。" +
                "就业替代效应引发了人们对未来工作的担忧，需要社会各界共同应对。" +
                "人工智能的可解释性和透明度也是当前研究的热点问题。" +
                "面向未来，人工智能技术将继续快速发展。" +
                "多模态学习将使AI系统能够同时处理文本、图像、音频等多种类型的数据。" +
                "强化学习技术将在游戏、机器人控制等领域发挥更大作用。" +
                "联邦学习等隐私保护技术将在保护数据隐私的同时实现模型训练。" +
                "边缘计算的发展将使AI应用能够在资源受限的设备上高效运行。" +
                "量子计算的成熟可能为某些AI算法带来指数级的性能提升。" +
                "人工智能与其他前沿技术的融合将创造出更多创新应用和商业机会。" +
                "总的来说，人工智能正在重塑我们的世界，为人类社会带来前所未有的变革和机遇。";
        
        System.out.println("=== 超长文本拆句测试 ===");
        System.out.println("原文长度: " + longText.length() + " 字符");
        System.out.println("原文汉字数: " + countChinese(longText));
        System.out.println();
        
        List<String> result = TextSplitter.splitText(longText);
        TextSplitter.printStatistics(longText, result);
        
        System.out.println("\n" + "=".repeat(80) + "\n");
        
        // 验证拆句结果是否符合要求
        validateResults(result);
    }
    
    /**
     * 验证拆句结果是否符合要求
     */
    private static void validateResults(List<String> sentences) {
        System.out.println("=== 拆句结果验证 ===");
        
        boolean allValid = true;
        int totalChinese = 0;
        
        for (int i = 0; i < sentences.size(); i++) {
            String sentence = sentences.get(i);
            int chineseCount = countChinese(sentence);
            totalChinese += chineseCount;
            
            boolean lengthValid = chineseCount <= 400;
            boolean minLengthValid = chineseCount >= 10;
            
            System.out.printf("第%d句: %d字 - 长度检查: %s, 最小长度检查: %s%n", 
                    i + 1, chineseCount, 
                    lengthValid ? "✓" : "✗", 
                    minLengthValid ? "✓" : "✗");
            
            if (!lengthValid || !minLengthValid) {
                allValid = false;
            }
        }
        
        System.out.println();
        System.out.println("总句数: " + sentences.size());
        System.out.println("总汉字数: " + totalChinese);
        System.out.println("验证结果: " + (allValid ? "✓ 所有句子都符合要求" : "✗ 存在不符合要求的句子"));
    }
    
    /**
     * 统计文本中的汉字数量
     */
    private static int countChinese(String text) {
        if (text == null) {
            return 0;
        }
        
        int count = 0;
        for (char c : text.toCharArray()) {
            if (c >= 0x4e00 && c <= 0x9fa5) {
                count++;
            }
        }
        return count;
    }
}
