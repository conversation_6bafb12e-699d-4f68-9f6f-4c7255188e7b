package com.mantis;

import java.util.*;
import java.util.regex.Pattern;

/**
 * 文本拆句工具类
 * 功能：将长文本按照标点符号智能拆分成多个句子
 * 要求：
 * 1. 每句最大长度400汉字
 * 2. 尽量按照标点符号进行拆句，避免出现断句的情况
 * 3. 每部分至少10个汉字
 * 4. 句子尽可能少
 */
public class TextSplitter {
    
    // 主要标点符号（优先级高）
    private static final String PRIMARY_PUNCTUATION = "。！？；";
    
    // 次要标点符号（优先级中）
    private static final String SECONDARY_PUNCTUATION = "，、：";
    
    // 其他可拆分符号（优先级低）
    private static final String OTHER_PUNCTUATION = "）】」』\"》";
    
    // 汉字正则表达式
    private static final Pattern CHINESE_PATTERN = Pattern.compile("[\u4e00-\u9fa5]");
    
    // 配置参数
    private static final int MAX_LENGTH = 90;  // 每句最大汉字数
    private static final int MIN_LENGTH = 10;   // 每句最少汉字数
    
    /**
     * 拆分文本为句子列表
     * @param text 输入文本
     * @return 拆分后的句子列表
     */
    public static List<String> splitText(String text) {
        if (text == null || text.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        List<String> result = new ArrayList<>();
        String cleanText = text.trim();
        
        // 如果文本长度小于等于最大长度且汉字数大于等于最小长度，直接返回
        if (countChinese(cleanText) <= MAX_LENGTH && countChinese(cleanText) >= MIN_LENGTH) {
            result.add(cleanText);
            return result;
        }
        
        // 开始拆分处理
        splitTextRecursive(cleanText, result);
        
        // 后处理：合并过短的句子
        return postProcess(result);
    }
    
    /**
     * 递归拆分文本
     */
    private static void splitTextRecursive(String text, List<String> result) {
        if (text == null || text.trim().isEmpty()) {
            return;
        }
        
        text = text.trim();
        int chineseCount = countChinese(text);
        
        // 如果文本长度符合要求，直接添加
        if (chineseCount <= MAX_LENGTH && chineseCount >= MIN_LENGTH) {
            result.add(text);
            return;
        }
        
        // 如果文本太短，直接添加（后续会在后处理中合并）
        if (chineseCount < MIN_LENGTH) {
            result.add(text);
            return;
        }
        
        // 寻找最佳拆分点
        int splitPoint = findBestSplitPoint(text);
        
        if (splitPoint == -1) {
            // 没有找到合适的拆分点，强制拆分
            splitPoint = findForceSplitPoint(text);
        }
        
        if (splitPoint > 0 && splitPoint < text.length()) {
            String leftPart = text.substring(0, splitPoint + 1).trim();
            String rightPart = text.substring(splitPoint + 1).trim();
            
            // 递归处理左右两部分
            splitTextRecursive(leftPart, result);
            splitTextRecursive(rightPart, result);
        } else {
            // 无法拆分，直接添加
            result.add(text);
        }
    }
    
    /**
     * 寻找最佳拆分点
     */
    private static int findBestSplitPoint(String text) {
        int textLength = text.length();
        int targetPosition = Math.min(MAX_LENGTH, textLength / 2);
        
        // 在目标位置附近寻找最佳拆分点
        int searchRange = Math.min(100, textLength / 4);
        int startPos = Math.max(0, targetPosition - searchRange);
        int endPos = Math.min(textLength - 1, targetPosition + searchRange);
        
        // 优先级1：主要标点符号
        int bestPoint = findPunctuationInRange(text, startPos, endPos, PRIMARY_PUNCTUATION);
        if (bestPoint != -1 && isValidSplit(text, bestPoint)) {
            return bestPoint;
        }
        
        // 优先级2：次要标点符号
        bestPoint = findPunctuationInRange(text, startPos, endPos, SECONDARY_PUNCTUATION);
        if (bestPoint != -1 && isValidSplit(text, bestPoint)) {
            return bestPoint;
        }
        
        // 优先级3：其他标点符号
        bestPoint = findPunctuationInRange(text, startPos, endPos, OTHER_PUNCTUATION);
        if (bestPoint != -1 && isValidSplit(text, bestPoint)) {
            return bestPoint;
        }
        
        return -1;
    }
    
    /**
     * 在指定范围内寻找标点符号
     */
    private static int findPunctuationInRange(String text, int start, int end, String punctuation) {
        // 从中间向两边搜索，优先选择靠近中间的位置
        int mid = (start + end) / 2;
        
        for (int offset = 0; offset <= (end - start) / 2; offset++) {
            // 先检查中间偏右的位置
            int rightPos = mid + offset;
            if (rightPos <= end && punctuation.indexOf(text.charAt(rightPos)) != -1) {
                return rightPos;
            }
            
            // 再检查中间偏左的位置
            int leftPos = mid - offset;
            if (leftPos >= start && punctuation.indexOf(text.charAt(leftPos)) != -1) {
                return leftPos;
            }
        }
        
        return -1;
    }
    
    /**
     * 验证拆分点是否有效
     */
    private static boolean isValidSplit(String text, int splitPoint) {
        if (splitPoint <= 0 || splitPoint >= text.length() - 1) {
            return false;
        }
        
        String leftPart = text.substring(0, splitPoint + 1);
        String rightPart = text.substring(splitPoint + 1);
        
        int leftChinese = countChinese(leftPart);
        int rightChinese = countChinese(rightPart);
        
        // 两部分都不能超过最大长度，且至少有一部分满足最小长度要求
        return leftChinese <= MAX_LENGTH && rightChinese <= MAX_LENGTH && 
               (leftChinese >= MIN_LENGTH || rightChinese >= MIN_LENGTH);
    }
    
    /**
     * 强制拆分点（当找不到合适标点时）
     */
    private static int findForceSplitPoint(String text) {
        int chineseCount = 0;
        for (int i = 0; i < text.length(); i++) {
            if (CHINESE_PATTERN.matcher(String.valueOf(text.charAt(i))).matches()) {
                chineseCount++;
                if (chineseCount >= MAX_LENGTH - 10) { // 留一些缓冲
                    return i;
                }
            }
        }
        return text.length() / 2; // 默认从中间拆分
    }
    
    /**
     * 后处理：合并过短的句子
     */
    private static List<String> postProcess(List<String> sentences) {
        if (sentences.size() <= 1) {
            return sentences;
        }
        
        List<String> result = new ArrayList<>();
        StringBuilder currentSentence = new StringBuilder();
        
        for (String sentence : sentences) {
            if (sentence.trim().isEmpty()) {
                continue;
            }
            
            int currentChinese = countChinese(currentSentence.toString());
            int sentenceChinese = countChinese(sentence);
            
            // 如果当前累积句子为空，直接添加
            if (currentSentence.length() == 0) {
                currentSentence.append(sentence);
            }
            // 如果合并后不超过最大长度，且当前句子太短，则合并
            else if (currentChinese + sentenceChinese <= MAX_LENGTH && 
                     (currentChinese < MIN_LENGTH || sentenceChinese < MIN_LENGTH)) {
                currentSentence.append(sentence);
            }
            // 否则，保存当前句子，开始新句子
            else {
                if (currentSentence.length() > 0) {
                    result.add(currentSentence.toString().trim());
                }
                currentSentence = new StringBuilder(sentence);
            }
        }
        
        // 添加最后一个句子
        if (currentSentence.length() > 0) {
            result.add(currentSentence.toString().trim());
        }
        
        return result;
    }
    
    /**
     * 统计文本中的汉字数量
     */
    private static int countChinese(String text) {
        if (text == null) {
            return 0;
        }
        
        int count = 0;
        for (char c : text.toCharArray()) {
            if (CHINESE_PATTERN.matcher(String.valueOf(c)).matches()) {
                count++;
            }
        }
        return count;
    }
    
    /**
     * 打印拆分结果的统计信息
     */
    public static void printStatistics(String originalText, List<String> sentences) {
        System.out.println("=== 文本拆句统计 ===");
        System.out.println("原文汉字数: " + countChinese(originalText));
        System.out.println("拆分句子数: " + sentences.size());
        System.out.println();
        
        for (int i = 0; i < sentences.size(); i++) {
            String sentence = sentences.get(i);
            System.out.println("第" + (i + 1) + "句 (" + countChinese(sentence) + "字): " + sentence);
        }
    }
}
