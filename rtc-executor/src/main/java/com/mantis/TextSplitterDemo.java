package com.mantis;

import java.util.List;

/**
 * 文本拆句工具演示类
 */
public class TextSplitterDemo {
    
    public static void main(String[] args) {
        // 测试用例1：包含多种标点符号的长文本
        String text1 = "人工智能技术的发展日新月异，深度学习、机器学习等技术不断突破。在自然语言处理领域，大型语言模型如GPT、BERT等展现出了惊人的能力，它们能够理解和生成人类语言，在翻译、问答、文本生成等任务中表现优异。同时，计算机视觉技术也取得了重大进展，图像识别、目标检测、人脸识别等应用已经广泛应用于各个行业。此外，语音识别和语音合成技术的成熟，使得人机交互变得更加自然和便捷。随着5G网络的普及和边缘计算技术的发展，人工智能将在更多场景中发挥重要作用，推动社会的数字化转型和智能化升级。";
        
        System.out.println("=== 测试用例1 ===");
        System.out.println("原文: " + text1);
        System.out.println();
        
        List<String> result1 = TextSplitter.splitText(text1);
        TextSplitter.printStatistics(text1, result1);
        
        System.out.println("\n" + "=".repeat(50) + "\n");
        
        // 测试用例2：短文本
        String text2 = "这是一个短文本测试。";
        
        System.out.println("=== 测试用例2 ===");
        System.out.println("原文: " + text2);
        System.out.println();
        
        List<String> result2 = TextSplitter.splitText(text2);
        TextSplitter.printStatistics(text2, result2);
        
        System.out.println("\n" + "=".repeat(50) + "\n");
        
        // 测试用例3：包含对话的文本
        String text3 = "张三问道：\"你今天去哪里了？\"李四回答说：\"我去了图书馆，借了几本关于人工智能的书籍。\"张三继续问：\"那些书怎么样？\"李四说：\"非常不错，特别是那本《深度学习》，里面的内容很详细，从基础概念到实际应用都有涉及。我觉得对我的研究很有帮助。\"张三点点头说：\"那我也去借一本来看看。\"";
        
        System.out.println("=== 测试用例3 ===");
        System.out.println("原文: " + text3);
        System.out.println();
        
        List<String> result3 = TextSplitter.splitText(text3);
        TextSplitter.printStatistics(text3, result3);
        
        System.out.println("\n" + "=".repeat(50) + "\n");
        
        // 测试用例4：没有标点符号的长文本
        String text4 = "这是一段没有标点符号的很长很长的文本内容用来测试当文本中缺少标点符号时程序是否能够正确处理并进行强制拆分以确保每个句子的长度都在合理范围内同时尽量保持语义的完整性这种情况在实际应用中可能会遇到所以需要有相应的处理机制来应对这种特殊情况确保程序的健壮性和实用性";
        
        System.out.println("=== 测试用例4 ===");
        System.out.println("原文: " + text4);
        System.out.println();
        
        List<String> result4 = TextSplitter.splitText(text4);
        TextSplitter.printStatistics(text4, result4);
        
        System.out.println("\n" + "=".repeat(50) + "\n");
        
        // 测试用例5：混合中英文的文本
        String text5 = "随着AI技术的发展，ChatGPT、Claude等大型语言模型展现出了强大的能力。这些模型基于Transformer架构，通过大规模预训练获得了丰富的知识。在NLP任务中，它们能够进行文本生成、翻译、摘要等多种操作。同时，这些模型也在不断改进，GPT-4相比GPT-3.5有了显著提升。未来，随着计算能力的增强和算法的优化，AI将在更多领域发挥重要作用。";
        
        System.out.println("=== 测试用例5 ===");
        System.out.println("原文: " + text5);
        System.out.println();
        
        List<String> result5 = TextSplitter.splitText(text5);
        TextSplitter.printStatistics(text5, result5);
    }
}
