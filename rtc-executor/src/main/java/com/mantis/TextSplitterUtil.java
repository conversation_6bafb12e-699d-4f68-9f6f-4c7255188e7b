package com.mantis;

import java.util.List;

/**
 * 文本拆句工具类 - 简化版API
 * 提供更简洁的接口供外部调用
 */
public class TextSplitterUtil {
    
    /**
     * 拆分文本为句子列表（使用默认配置）
     * @param text 输入文本
     * @return 拆分后的句子列表
     */
    public static List<String> split(String text) {
        return TextSplitter.splitText(text);
    }
    
    /**
     * 拆分文本并返回拆分统计信息
     * @param text 输入文本
     * @return 拆分结果对象
     */
    public static SplitResult splitWithStats(String text) {
        List<String> sentences = TextSplitter.splitText(text);
        return new SplitResult(text, sentences);
    }
    
    /**
     * 拆分结果类
     */
    public static class SplitResult {
        private final String originalText;
        private final List<String> sentences;
        private final int originalChineseCount;
        private final int sentenceCount;
        
        public SplitResult(String originalText, List<String> sentences) {
            this.originalText = originalText;
            this.sentences = sentences;
            this.originalChineseCount = countChinese(originalText);
            this.sentenceCount = sentences.size();
        }
        
        public String getOriginalText() {
            return originalText;
        }
        
        public List<String> getSentences() {
            return sentences;
        }
        
        public int getOriginalChineseCount() {
            return originalChineseCount;
        }
        
        public int getSentenceCount() {
            return sentenceCount;
        }
        
        /**
         * 获取每个句子的汉字数量
         */
        public int[] getSentenceLengths() {
            int[] lengths = new int[sentences.size()];
            for (int i = 0; i < sentences.size(); i++) {
                lengths[i] = countChinese(sentences.get(i));
            }
            return lengths;
        }
        
        /**
         * 验证所有句子是否符合长度要求
         */
        public boolean isValid() {
            for (String sentence : sentences) {
                int chineseCount = countChinese(sentence);
                if (chineseCount > 400 || chineseCount < 10) {
                    return false;
                }
            }
            return true;
        }
        
        /**
         * 打印统计信息
         */
        public void printStats() {
            System.out.println("=== 文本拆句统计 ===");
            System.out.println("原文汉字数: " + originalChineseCount);
            System.out.println("拆分句子数: " + sentenceCount);
            System.out.println("验证结果: " + (isValid() ? "✓ 符合要求" : "✗ 不符合要求"));
            System.out.println();
            
            int[] lengths = getSentenceLengths();
            for (int i = 0; i < sentences.size(); i++) {
                System.out.println("第" + (i + 1) + "句 (" + lengths[i] + "字): " + sentences.get(i));
            }
        }
        
        /**
         * 获取简要统计信息
         */
        public String getSummary() {
            return String.format("原文%d字，拆分为%d句，平均每句%.1f字", 
                    originalChineseCount, sentenceCount, 
                    (double) originalChineseCount / sentenceCount);
        }
        
        private int countChinese(String text) {
            if (text == null) {
                return 0;
            }
            
            int count = 0;
            for (char c : text.toCharArray()) {
                if (c >= 0x4e00 && c <= 0x9fa5) {
                    count++;
                }
            }
            return count;
        }
    }
    
    /**
     * 批量拆分多个文本
     * @param texts 文本数组
     * @return 拆分结果数组
     */
    public static SplitResult[] batchSplit(String... texts) {
        SplitResult[] results = new SplitResult[texts.length];
        for (int i = 0; i < texts.length; i++) {
            results[i] = splitWithStats(texts[i]);
        }
        return results;
    }
    
    /**
     * 检查文本是否需要拆分
     * @param text 输入文本
     * @return 如果文本汉字数超过400或少于10，返回true
     */
    public static boolean needsSplit(String text) {
        int chineseCount = countChinese(text);
        return chineseCount > 400 || chineseCount < 10;
    }
    
    /**
     * 统计文本中的汉字数量
     */
    private static int countChinese(String text) {
        if (text == null) {
            return 0;
        }
        
        int count = 0;
        for (char c : text.toCharArray()) {
            if (c >= 0x4e00 && c <= 0x9fa5) {
                count++;
            }
        }
        return count;
    }
}
