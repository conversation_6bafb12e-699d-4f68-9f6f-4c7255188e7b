package com.mantis;

import java.util.List;

/**
 * 文本拆句工具类使用示例
 */
public class TextSplitterUtilDemo {
    
    public static void main(String[] args) {
        // 示例1：简单使用
        String text1 = "这是一个测试文本，用来演示文本拆句功能。程序会根据标点符号和长度要求来拆分文本。";
        
        System.out.println("=== 示例1：简单使用 ===");
        List<String> sentences = TextSplitterUtil.split(text1);
        System.out.println("原文: " + text1);
        System.out.println("拆分结果:");
        for (int i = 0; i < sentences.size(); i++) {
            System.out.println("  " + (i + 1) + ". " + sentences.get(i));
        }
        
        System.out.println("\n" + "=".repeat(60) + "\n");
        
        // 示例2：带统计信息的使用
        String text2 = "人工智能是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。" +
                "该领域的研究包括机器人、语言识别、图像识别、自然语言处理和专家系统等。" +
                "人工智能从诞生以来，理论和技术日益成熟，应用领域也不断扩大。" +
                "可以设想，未来人工智能带来的科技产品，将会是人类智慧的容器。" +
                "人工智能可以对人的意识、思维的信息过程的模拟。" +
                "人工智能不是人的智能，但能像人那样思考、也可能超过人的智能。";
        
        System.out.println("=== 示例2：带统计信息的使用 ===");
        TextSplitterUtil.SplitResult result = TextSplitterUtil.splitWithStats(text2);
        result.printStats();
        System.out.println("\n简要统计: " + result.getSummary());
        
        System.out.println("\n" + "=".repeat(60) + "\n");
        
        // 示例3：批量处理
        String[] texts = {
                "短文本测试。",
                "这是一个中等长度的文本，包含了一些基本的信息和描述。",
                "这是一个比较长的文本示例，用来测试程序在处理较长文本时的表现。文本中包含了多个句子，有不同的标点符号，程序需要根据这些标点符号来进行合理的拆分。同时还要确保每个拆分后的句子长度都在规定的范围内，既不能太长也不能太短。"
        };
        
        System.out.println("=== 示例3：批量处理 ===");
        TextSplitterUtil.SplitResult[] results = TextSplitterUtil.batchSplit(texts);
        
        for (int i = 0; i < results.length; i++) {
            System.out.println("文本" + (i + 1) + ": " + results[i].getSummary());
            System.out.println("  需要拆分: " + TextSplitterUtil.needsSplit(texts[i]));
            System.out.println("  验证结果: " + (results[i].isValid() ? "✓" : "✗"));
            System.out.println();
        }
        
        System.out.println("=".repeat(60) + "\n");
        
        // 示例4：实际应用场景
        System.out.println("=== 示例4：实际应用场景 ===");
        String articleText = "随着科技的飞速发展，人工智能已经成为当今世界最热门的话题之一。" +
                "从智能手机的语音助手到自动驾驶汽车，从医疗诊断到金融风控，AI技术正在改变着我们生活的方方面面。" +
                "然而，在享受AI带来便利的同时，我们也需要思考其潜在的风险和挑战。" +
                "如何确保AI系统的安全性和可靠性？如何处理算法偏见和隐私保护问题？" +
                "这些都是我们在AI发展道路上必须面对和解决的重要问题。" +
                "只有在技术进步和伦理规范之间找到平衡，我们才能真正实现AI技术的可持续发展，" +
                "让人工智能更好地服务于人类社会的进步和发展。";
        
        // 检查是否需要拆分
        if (TextSplitterUtil.needsSplit(articleText)) {
            System.out.println("检测到文本需要拆分，正在处理...");
            TextSplitterUtil.SplitResult articleResult = TextSplitterUtil.splitWithStats(articleText);
            
            System.out.println("\n处理结果:");
            System.out.println(articleResult.getSummary());
            
            System.out.println("\n拆分后的句子:");
            List<String> articleSentences = articleResult.getSentences();
            for (int i = 0; i < articleSentences.size(); i++) {
                System.out.println((i + 1) + ". " + articleSentences.get(i));
                System.out.println("   [" + articleResult.getSentenceLengths()[i] + "字]");
                System.out.println();
            }
        } else {
            System.out.println("文本长度合适，无需拆分。");
        }
    }
}
