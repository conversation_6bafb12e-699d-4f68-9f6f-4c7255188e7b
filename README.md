# micro-rtc-service
这是一个实时智能语音对话系统，主要实现以下核心功能：

1. 实时通信(RTC)服务 - 通过`rtc`相关模块提供实时语音通话能力
2. 语音识别(ASR)服务 - 集成了阿里云、腾讯云和火山引擎的语音识别能力
3. 语音合成(TTS)服务 - 支持多种TTS引擎，包括:
   - CosyVoice
   - MiniMax
   - 火山引擎
4. 大语言模型(LLM)集成 - 用于智能对话能力
5. 呼叫中心对接 - 通过`tel`服务模块实现
6. 语音活动检测(VAD) - 通过`slierovad`模块实现

项目架构设计重点考虑了高性能、高可用、低延迟和低内存占用的特性，这对于实时语音通信系统至关重要。

从代码组织上看，项目采用了标准的Java服务架构，包含:
- controller层：处理API请求
- service层：实现核心业务逻辑
- pojo层：定义数据模型
- utils：提供工具类支持
- config：系统配置

项目还集成了多种第三方服务，通过不同的适配器实现，使系统能够灵活切换不同的语音识别、语音合成和大模型提供商。这种架构设计使得系统具有良好的扩展性和可维护性，能够满足实时智能语音对话的高性能需求。


## 库依赖
- libJVolcEngineRTC.so
- libRTCFFmpeg.so
- libVolcEngineRTC.so

## Java 依赖
- VolcEngineRTC.jar

## JVM
JDK11


