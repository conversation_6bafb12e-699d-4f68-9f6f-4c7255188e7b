<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.mantis</groupId>
    <artifactId>micro-rtc-service</artifactId>
    <version>1.0-SNAPSHOT</version>
    <packaging>pom</packaging>

    <properties>
        <java.version>11</java.version>
        <vaadin.version>14.3.4</vaadin.version>
        <global-common.version>0.0.1-SNAPSHOT</global-common.version>
        <spring-cloud.version>Hoxton.SR9</spring-cloud.version>
        <spring-cloud-alibaba.version>2.2.1.RELEASE</spring-cloud-alibaba.version>
        <spring-boot.version>2.3.7.RELEASE</spring-boot.version>
        <mybatis.version>2.1.3</mybatis.version>
        <druid-spring-boot-starter.version>1.1.22</druid-spring-boot-starter.version>
        <platform-parent.version>5.0.0-SNAPSHOT</platform-parent.version>
        <retrofit2.version>2.9.0</retrofit2.version>
    </properties>

    <!--模块信息 -->
    <modules>
        <module>rtc-executor</module>
    </modules>

    <dependencies>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-devtools</artifactId>
            <scope>runtime</scope>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

    </dependencies>

    <!-- 资源仓库配置 -->
    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>${spring-cloud-alibaba.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.mybatis.spring.boot</groupId>
                <artifactId>mybatis-spring-boot-starter</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>druid-spring-boot-starter</artifactId>
                <version>${druid-spring-boot-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>com.mantis.platform</groupId>
                <artifactId>platform-common-basepojo</artifactId>
                <version>${platform-parent.version}</version>
            </dependency>

            <dependency>
                <groupId>com.mantis.micor</groupId>
                <artifactId>global-common</artifactId>
                <version>${global-common.version}</version>
            </dependency>
            <dependency>
                <groupId>com.mantis.micor</groupId>
                <artifactId>global-common-data-redis</artifactId>
                <version>${global-common.version}</version>
            </dependency>
<!--            <dependency>-->
<!--                <groupId>com.mantis.micor</groupId>-->
<!--                <artifactId>global-common-encrypt</artifactId>-->
<!--                <version>${global-common.version}</version>-->
<!--            </dependency>-->

            <dependency>
                <groupId>com.bytedance.rtc</groupId>
                <artifactId>VolcEngineRTC</artifactId>
                <version>20240925112900</version>
            </dependency>

            <dependency>
                <groupId>com.volcengine</groupId>
                <artifactId>volc-sdk-java</artifactId>
                <version>1.0.196</version>
            </dependency>

<!--            <dependency>-->
<!--                <groupId>com.mantis.micor</groupId>-->
<!--                <artifactId>global-common-mq-rocketmq-aliyun</artifactId>-->
<!--                <version>0.0.1-SNAPSHOT</version>-->
<!--            </dependency>-->

            <dependency>
                <groupId>com.squareup.retrofit2</groupId>
                <artifactId>retrofit</artifactId>
                <version>${retrofit2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.retrofit2</groupId>
                <artifactId>converter-jackson</artifactId>
                <version>${retrofit2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.squareup.retrofit2</groupId>
                <artifactId>adapter-rxjava2</artifactId>
                <version>${retrofit2.version}</version>
            </dependency>


<!--            <dependency>-->
<!--                <groupId>org.eclipse.paho</groupId>-->
<!--                <artifactId>org.eclipse.paho.client.mqttv3</artifactId>-->
<!--                <version>1.2.2</version>-->
<!--            </dependency>-->

<!--            <dependency>-->
<!--                <groupId>com.aliyun.openservices</groupId>-->
<!--                <artifactId>ons-client</artifactId>-->
<!--                <version>1.7.9.Final</version>-->
<!--            </dependency>-->

        </dependencies>
    </dependencyManagement>


    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>2.0.2</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                </configuration>
            </plugin>
        </plugins>
    </build>

    <repositories>
        <repository>
            <id>nexus-snapshots</id>
            <name>Nexus Snapshot Repository</name>
            <url>http://nexus.bjmantis.net/nexus/content/groups/public</url>
            <layout>default</layout>
            <snapshots>
                <enabled>true</enabled>
                <updatePolicy>interval:60</updatePolicy>
            </snapshots>
        </repository>

        <repository>
            <id>spring-libs-snapshot</id>
            <name>Spring Snapshot Repository</name>
            <url>http://repo.spring.io/libs-snapshot</url>
        </repository>
    </repositories>

</project>