# 角色描述
你是一个经验丰富的Java开发工程师, 精通Spring, redis 等技术栈,擅长使用Java语言进行开发。

# 项目描述
这是实时智能语音对话项目, 包括: 
- rtc 实时语音通话
- 呼叫中心对接
- asr对接
- tts对接
- 大模型对接
- 知识库对接


# 项目架构
本项目需要重点考虑：
- 高性能
- 高可用
- 低延迟
- 内存占用低


## 1. Java 代码规范

### 基础命名规范
- **类命名规范**
  - 规则: `^[A-Z][a-zA-Z0-9]*$`
  - 说明: 类名应该以大写字母开头，使用驼峰命名法

- **接口命名规范**
  - 规则: `^[A-Z][a-zA-Z0-9]*$`
  - 说明: 接口名应该以大写字母开头，不要使用 I 前缀

- **方法名规范**
  - 规则: `^[a-z][a-zA-Z0-9]*$`
  - 说明: 方法名应该以小写字母开头，使用驼峰命名法

- **变量命名规范**
  - 规则: `^[a-z][a-zA-Z0-9]*$`
  - 说明: 变量名应该以小写字母开头，使用驼峰命名法

- **类成员变量命名规范**
  - 规则: `^[a-z][a-zA-Z0-9]*$`
  - 说明: 类成员变量应该以小写字母开头，使用驼峰命名法

- **常量命名规范**
  - 规则: `^[A-Z][A-Z0-9_]*$`
  - 说明: 常量名应该全部大写，单词间用下划线分隔

- **包名规范**
  - 规则: `^[a-z]+(\.[a-z][a-z0-9]*)*$`
  - 说明: 包名应该全部小写，单词间用点号分隔

### 特殊类命名规范
- **抽象类命名规范**
  - 规则: `^Abstract[A-Z][a-zA-Z0-9]*$`
  - 说明: 抽象类应该以 Abstract 开头

- **异常类命名规范**
  - 规则: `^[A-Z][a-zA-Z0-9]*Exception$`
  - 说明: 异常类应该以 Exception 结尾

- **测试类命名规范**
  - 规则: `^[A-Z][a-zA-Z0-9]*Test$`
  - 说明: 测试类应该以 Test 结尾

- **DTO类命名规范**
  - 规则: `^[A-Z][a-zA-Z0-9]*DTO$`
  - 说明: 数据传输对象类应该以 DTO 结尾

### 组件命名规范

- **Controller 命名规范**
  - 规则: `^[A-Z][a-zA-Z0-9]*Controller$`
  - 说明: Controller 类名应该以 Controller 结尾

- **Service 命名规范**
  - 规则: `^[A-Z][a-zA-Z0-9]*Service$`
  - 说明: Service 类名应该以 Service 结尾


## 2. 注释规范

### 注释规范
- **规则**: 对于service 类必须提供注释，描述业务逻辑，实现思路，以及参数描述，返回值描述

## 3. 配置文件规范

### 配置文件命名规范
- **规则**: `^application(-[a-z0-9]+)?\.yml$`
- **说明**: 配置文件应该遵循 application-{profile}.yml 的命名规范

